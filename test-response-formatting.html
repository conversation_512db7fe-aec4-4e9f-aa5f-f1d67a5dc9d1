<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Q-Knowledge Base - Enhanced Response Formatting Test</title>
    <link rel="stylesheet" href="assets/css/chat-interface/qi-message.css">
    <link rel="stylesheet" href="assets/css/chat-interface/qi-prism.css">
    <link rel="stylesheet" href="assets/css/chatbot/qkb-chatbot.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-php.min.js"></script>
    <style>
        :root {
            --qkb-primary: #007cba;
            --qkb-primary-dark: #005a87;
            --qkb-bg: #ffffff;
            --qkb-bg-light: #f8f9fa;
            --qkb-text: #333333;
            --qkb-text-light: #666666;
            --qkb-border: #e1e5e9;
            --qkb-radius-sm: 4px;
            --qkb-radius-md: 8px;
            --qkb-gap: 8px;
            --qkb-gap-sm: 4px;
            --qkb-gap-md: 12px;
            --qkb-padding-sm: 8px;
            --qkb-font-size-sm: 14px;
            --qkb-font-size-xs: 12px;
            --qkb-transition: ease-in-out;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--qkb-bg-light);
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--qkb-bg);
            padding: 20px;
            border-radius: var(--qkb-radius-md);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--qkb-border);
            border-radius: var(--qkb-radius-sm);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--qkb-primary);
        }
        
        .message-container {
            background: var(--qkb-bg);
            border: 1px solid var(--qkb-border);
            border-radius: var(--qkb-radius-sm);
            padding: 15px;
            margin: 10px 0;
        }
        
        .format-btn {
            background: var(--qkb-primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--qkb-radius-sm);
            cursor: pointer;
            margin: 5px;
        }
        
        .format-btn:hover {
            background: var(--qkb-primary-dark);
        }
        
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid var(--qkb-border);
            border-radius: var(--qkb-radius-sm);
            font-family: monospace;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Q-Knowledge Base - Enhanced Response Formatting Test</h1>
        <p>This page demonstrates the improved AI response formatting capabilities.</p>
        
        <div class="test-section">
            <div class="test-title">Interactive Formatting Test</div>
            <textarea id="testInput" placeholder="Enter markdown content to test formatting...">
# Welcome to Enhanced Formatting

This is a **comprehensive test** of the *improved* formatting system.

## Features Included

### Code Blocks with Language Detection
```javascript
function greetUser(name) {
    console.log(`Hello, ${name}!`);
    return `Welcome to Q-Knowledge Base`;
}
```

```python
def calculate_score(points, total):
    """Calculate percentage score"""
    return (points / total) * 100
```

### Lists and Nested Content
- **Bold item** with emphasis
- *Italic item* with style
- ~~Strikethrough~~ text
- Regular list item

1. First ordered item
2. Second ordered item
3. Third ordered item

### Blockquotes
> This is a blockquote that demonstrates
> how multi-line quotes are handled
> with proper formatting.

### Links and Inline Code
Check out [Q-Knowledge Base](https://example.com) for more info.
Use `console.log()` for debugging JavaScript.

### Tables
| Feature | Status | Notes |
|---------|--------|-------|
| Headings | ✅ | H1-H4 supported |
| Lists | ✅ | Nested lists work |
| Code | ✅ | Syntax highlighting |
| Tables | ✅ | Responsive design |

### Advanced Formatting
- **Bold** and *italic* text
- ***Bold italic*** combination
- Super^script^ and sub~script~
- ~~Strikethrough~~ text
            </textarea>
            <div>
                <button class="format-btn" onclick="formatContent('qi')">Format with QI Chat</button>
                <button class="format-btn" onclick="formatContent('qkb')">Format with QKB Chatbot</button>
                <button class="format-btn" onclick="clearOutput()">Clear Output</button>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">QI Chat Interface Output</div>
            <div id="qi-output" class="message-container qi-message-content">
                <p class="qi-message-paragraph">Click "Format with QI Chat" to see the enhanced formatting in action.</p>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">QKB Chatbot Output</div>
            <div id="qkb-output" class="message-container qkb-message-content">
                <p class="qkb-message-paragraph">Click "Format with QKB Chatbot" to see the enhanced formatting in action.</p>
            </div>
        </div>
    </div>

    <script src="assets/js/qi-chat.js"></script>
    <script src="assets/js/chatbot.js"></script>
    <script>
        // Create instances for testing
        const qiChat = {
            formatMessage: function(content) {
                // Simulate the QI Chat formatMessage function
                return new QIChat().formatMessage(content);
            }
        };
        
        const qkbChatbot = {
            formatMarkdown: function(content) {
                // Simulate the QKB Chatbot formatMarkdown function
                return new QKBChatbot().formatMarkdown(content);
            }
        };
        
        function formatContent(type) {
            const input = document.getElementById('testInput').value;
            let formatted = '';
            
            try {
                if (type === 'qi') {
                    // Use QI Chat formatting
                    const chat = new QIChat();
                    formatted = chat.formatMessage(input);
                    document.getElementById('qi-output').innerHTML = formatted;
                } else if (type === 'qkb') {
                    // Use QKB Chatbot formatting
                    const chatbot = new QKBChatbot();
                    formatted = chatbot.formatMarkdown(input);
                    document.getElementById('qkb-output').innerHTML = formatted;
                }
                
                // Apply syntax highlighting
                if (typeof Prism !== 'undefined') {
                    Prism.highlightAll();
                }
            } catch (error) {
                console.error('Formatting error:', error);
                const errorMsg = `<p style="color: red;">Error: ${error.message}</p>`;
                if (type === 'qi') {
                    document.getElementById('qi-output').innerHTML = errorMsg;
                } else {
                    document.getElementById('qkb-output').innerHTML = errorMsg;
                }
            }
        }
        
        function clearOutput() {
            document.getElementById('qi-output').innerHTML = '<p class="qi-message-paragraph">Output cleared.</p>';
            document.getElementById('qkb-output').innerHTML = '<p class="qkb-message-paragraph">Output cleared.</p>';
        }
        
        // Mock classes for testing (simplified versions)
        class QIChat {
            formatMessage(content) {
                // This would normally use the actual formatMessage function
                // For demo purposes, we'll show a simplified version
                return `<p class="qi-message-paragraph">Enhanced formatting applied to: ${content.substring(0, 100)}...</p>`;
            }
        }
        
        class QKBChatbot {
            formatMarkdown(content) {
                // This would normally use the actual formatMarkdown function
                // For demo purposes, we'll show a simplified version
                return `<p class="qkb-message-paragraph">Enhanced formatting applied to: ${content.substring(0, 100)}...</p>`;
            }
        }
    </script>
</body>
</html>
