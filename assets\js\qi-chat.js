/**
 * QiChat - Enhanced Chat Interface
 *
 *
 * @version 2.1.0
 * <AUTHOR> Base Team
 */

if (typeof jQuery === "undefined") {
  console.error("jQuery is required for QiChat to work!");
  throw new Error("jQuery is required for QiChat to work!");
}

(function ($) {
  "use strict";

  /**
   * QiChat - Advanced Chat Interface Class
   *
   * Handles all chat functionality including messaging, file uploads,
   * assistant management, and user interactions.
   *
   * @class QiChat
   * @version 2.1.0
   */
  class QiChat {
    /**
     * Initialize the QiChat instance
     * @constructor
     */
    constructor() {
      // Core state management
      this.chatHistory = {};
      this.suggestionTimeout = null;
      this.currentChatId = null;
      this.username = qkbChatbot?.user_name || "there";
      this.selectedFile = null;

      // Performance and UI state
      this.isTyping = false;
      this.isEditing = false;
      this.messageQueue = [];
      this.processingQueue = false;
      this.currentAssistant = null;
      this.isSidebarHidden = false;

      // Configuration
      this.maxCharacters = 500;
      this.debounceDelay = 300;
      this.retryAttempts = 3;
      this.retryDelay = 1000;

      // Request management
      this.lastRequestTime = 0;
      this.minRequestInterval = 1000; // Minimum 1 second between requests
      this.pendingRequest = null;
      this.requestQueue = [];

      // Cleanup tracking with modern approaches
      this.eventListeners = new Map();
      this.timeouts = new Set();
      this.intervals = new Set();
      this.abortControllers = new Set();
      this.requestCache = new Map();

      // Performance monitoring
      this.performanceMetrics = {
        messagesSent: 0,
        averageResponseTime: 0,
        errorCount: 0,
        lastActivity: Date.now()
      };

      // Feature detection
      this.features = {
        clipboard: !!navigator.clipboard,
        resizeObserver: !!window.ResizeObserver,
        intersectionObserver: !!window.IntersectionObserver,
        abortController: !!window.AbortController,
        requestIdleCallback: !!window.requestIdleCallback
      };

      // Initialize when DOM is ready
      $(document).ready(() => {
        this.initialize().catch(error => {
          console.error('Failed to initialize QiChat:', error);
          this.handleInitializationError(error);
        });
      });
    }

    /**
     * Initialize the chat interface
     * Sets up all components, events, and initial state
     *
     * @returns {Promise<void>}
     */
    async initialize() {
      try {
        // Initialize core components first
        await this.initializeComponents();

        if (!this.$container?.length) {
          console.warn("Chat container not found - initialization aborted");
          return;
        }

        // Set up event handlers with proper cleanup tracking
        this.bindEvents();

        // Initialize state management
        await this.initializeState();

        // Load user data and preferences
        await this.loadUserPreferences();

        // Set up UI components
        this.initializeUI();

        // Initialize responsive behavior
        this.setupResponsiveHandling();

        // Handle fullscreen default setting
        this.handleFullscreenDefault();

        console.log("QiChat initialized successfully");

      } catch (error) {
        console.error("Failed to initialize QiChat:", error);
        this.handleInitializationError(error);
      }
    }

    /**
     * Initialize state management
     * @private
     */
    async initializeState() {
      this.loadChatHistory();
      await this.initializeAssistantSelector();
    }

    /**
     * Load user preferences and settings
     * @private
     */
    async loadUserPreferences() {
      // This can be extended to load user-specific settings
      // For now, we'll keep the existing behavior
    }

    /**
     * Initialize UI components
     * @private
     */
    initializeUI() {
      this.initSuggestedPrompts();
      this.adjustMessageContainerHeight();
    }

    /**
     * Set up responsive handling with performance optimizations
     * @private
     */
    setupResponsiveHandling() {
      this.handleResize();

      // Use ResizeObserver for better performance if available
      if (this.features.resizeObserver) {
        this.resizeObserver = new ResizeObserver(
          this.debounce(() => this.handleResize(), this.debounceDelay)
        );
        this.resizeObserver.observe(this.$container[0]);
      }

      // Set up intersection observer for virtual scrolling
      if (this.features.intersectionObserver) {
        this.setupVirtualScrolling();
      }

      // Set up accessibility features
      this.setupAccessibility();
    }

    /**
     * Set up virtual scrolling for better performance with large message lists
     * @private
     */
    setupVirtualScrolling() {
      this.intersectionObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            const $message = $(entry.target);
            if (entry.isIntersecting) {
              // Message is visible, ensure it's fully rendered
              $message.removeClass('qi-message-hidden');
            } else {
              // Message is not visible, can be optimized
              if (this.$messages.children().length > 50) {
                $message.addClass('qi-message-hidden');
              }
            }
          });
        },
        {
          root: this.$messages[0],
          rootMargin: '100px',
          threshold: 0.1
        }
      );
    }

    /**
     * Set up accessibility features
     * @private
     */
    setupAccessibility() {
      // Add ARIA live region for screen readers
      if (!this.$messages.attr('aria-live')) {
        this.$messages.attr('aria-live', 'polite');
        this.$messages.attr('aria-label', 'Chat conversation');
      }

      // Add keyboard navigation
      this.setupKeyboardNavigation();

      // Add focus management
      this.setupFocusManagement();
    }

    /**
     * Set up keyboard navigation
     * @private
     */
    setupKeyboardNavigation() {
      // Allow navigation through messages with arrow keys
      this.$messages.on('keydown', (e) => {
        const $messages = this.$messages.find('.qi-message');
        const $focused = $messages.filter(':focus');

        if ($focused.length === 0) return;

        const currentIndex = $messages.index($focused);
        let newIndex = currentIndex;

        switch (e.key) {
          case 'ArrowUp':
            newIndex = Math.max(0, currentIndex - 1);
            e.preventDefault();
            break;
          case 'ArrowDown':
            newIndex = Math.min($messages.length - 1, currentIndex + 1);
            e.preventDefault();
            break;
          case 'Home':
            newIndex = 0;
            e.preventDefault();
            break;
          case 'End':
            newIndex = $messages.length - 1;
            e.preventDefault();
            break;
        }

        if (newIndex !== currentIndex) {
          $messages.eq(newIndex).focus();
        }
      });
    }

    /**
     * Set up focus management for better accessibility
     * @private
     */
    setupFocusManagement() {
      // Initialize event system if not exists
      if (!this.events) {
        this.events = {};
      }

      // Store reference for cleanup
      this.accessibilityHandlers = {
        messageAdded: ($message) => {
          if ($message.hasClass('qi-assistant-message')) {
            // Briefly focus the new message for screen readers
            $message.attr('tabindex', '-1').focus();

            // Remove focus after announcement
            setTimeout(() => {
              $message.removeAttr('tabindex');
              this.$input.focus();
            }, 100);
          }
        },
        modalOpened: () => {
          this.lastFocusedElement = document.activeElement;
        },
        modalClosed: () => {
          if (this.lastFocusedElement) {
            this.lastFocusedElement.focus();
          }
        }
      };
    }

    /**
     * Simple event system for internal communication
     * @param {string} event - Event name
     * @param {Function} handler - Event handler
     */
    on(event, handler) {
      if (!this.events) {
        this.events = {};
      }
      if (!this.events[event]) {
        this.events[event] = [];
      }
      this.events[event].push(handler);
    }

    /**
     * Trigger an event
     * @param {string} event - Event name
     * @param {...any} args - Arguments to pass to handlers
     */
    trigger(event, ...args) {
      if (this.events && this.events[event]) {
        this.events[event].forEach(handler => {
          try {
            handler(...args);
          } catch (error) {
            console.error(`Error in event handler for ${event}:`, error);
          }
        });
      }
    }

    /**
     * Handle fullscreen default setting
     * @private
     */
    handleFullscreenDefault() {
      if (this.$container.attr("data-fullscreen-default") === "true") {
        this.$container.attr("data-fullscreen", true);
        $("body").addClass("qi-fullscreen-active");

        const timeoutId = setTimeout(() => {
          this.adjustMessageContainerHeight();
          this.scrollToBottom();
        }, 100);

        this.timeouts.add(timeoutId);
      }
    }

    /**
     * Handle initialization errors gracefully
     * @private
     * @param {Error} error - The initialization error
     */
    handleInitializationError(error) {
      // Show user-friendly error message
      if (this.$container?.length) {
        this.$container.html(`
          <div class="qi-error-state">
            <div class="qi-error-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="qi-error-message">
              <h3>Chat Initialization Failed</h3>
              <p>We're having trouble loading the chat interface. Please refresh the page and try again.</p>
              <button class="qi-retry-button" onclick="location.reload()">
                <i class="fas fa-redo"></i> Retry
              </button>
            </div>
          </div>
        `);
      }
    }

    /**
     * Initialize DOM components and cache jQuery objects
     * @returns {Promise<void>}
     */
    async initializeComponents() {
      try {
        // Cache main container
        this.$container = $(".qi-chat-container");
        if (!this.$container.length) {
          throw new Error("Chat container not found");
        }

        // Cache core UI elements
        this.cacheUIElements();

        // Initialize input configuration
        this.initializeInputConfiguration();

        // Set up sidebar if enabled
        this.initializeSidebar();

        // Initialize modals and overlays
        this.initializeModal();

        // Set up knowledge base components
        this.initializeKnowledgeBaseComponents();

        // Initialize character counter
        this.initializeCharacterCounter();

        console.log("Components initialized successfully");

      } catch (error) {
        console.error("Failed to initialize components:", error);
        throw error;
      }
    }

    /**
     * Cache frequently used UI elements with performance optimizations
     * @private
     */
    cacheUIElements() {
      // Use more specific selectors for better performance
      this.$messages = this.$container.children(".qi-main-chat").children(".qi-chat-wrapper").children(".qi-chat-messages");
      this.$messagesContainer = this.$messages; // Alias for compatibility
      this.$input = this.$container.find(".qi-input-container textarea").first();
      this.$sendButton = this.$container.find(".qi-send-button").first();
      this.$assistantItems = this.$container.find(".qi-assistant-item");
      this.$newChatButton = this.$container.find(".qi-new-chat").first();
      this.$clearHistoryButton = this.$container.find(".qi-clear-history").first();
      this.$toggleFullscreenButton = this.$container.find(".qi-toggle-fullscreen").first();

      // Cache additional frequently accessed elements
      this.$chatWrapper = this.$container.find(".qi-chat-wrapper").first();
      this.$inputContainer = this.$container.find(".qi-input-container").first();
      this.$sidebar = this.$container.find(".qi-sidebar").first();
      this.$mainChat = this.$container.find(".qi-main-chat").first();

      // Pre-cache common selectors for message operations
      this.messageSelectors = {
        userMessage: '.qi-user-message',
        assistantMessage: '.qi-assistant-message',
        messageContent: '.qi-message-content',
        messageActions: '.qi-message-actions',
        feedbackButtons: '.qi-feedback-button'
      };
    }

    /**
     * Initialize input field configuration
     * @private
     */
    initializeInputConfiguration() {
      if (this.$input.length) {
        this.$input.attr("data-placeholder-template", "How can I help you today?...");
        this.$input.attr("placeholder", "How can I help you today?...");
        this.$input.attr("autocomplete", "off");
        this.$input.attr("spellcheck", "true");
      }
    }

    /**
     * Initialize sidebar functionality
     * @private
     */
    initializeSidebar() {
      if (this.$container.attr("data-show-sidebar") === "true") {
        this.$toggleSidebarButton = this.$container.find(".qi-toggle-sidebar");
        this.isSidebarHidden = false;
        this.$container.attr("data-sidebar-hidden", this.isSidebarHidden);
      }
    }

    /**
     * Initialize knowledge base components
     * @private
     */
    initializeKnowledgeBaseComponents() {
      this.$addKnowledgeButton = this.$container.find(".qi-add-knowledge");
      this.$kbModal = $(".qi-kb-modal");
      this.$kbForm = this.$kbModal.find(".qi-kb-form");
      this.initializeKnowledgeBase();
    }

    /**
     * Initialize character counter
     * @private
     */
    initializeCharacterCounter() {
      this.$characterCount = $('<div class="qi-character-count">500</div>');
      const $inputContainer = this.$container.find(".qi-input-container");

      if ($inputContainer.length) {
        $inputContainer.append(this.$characterCount);
        this.updateCharacterCount();
      }
    }

    /**
     * Utility function for debouncing frequent operations
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func.apply(this, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    /**
     * Utility function for throttling operations
     * @param {Function} func - Function to throttle
     * @param {number} limit - Time limit in milliseconds
     * @returns {Function} Throttled function
     */
    throttle(func, limit) {
      let inThrottle;
      return function executedFunction(...args) {
        if (!inThrottle) {
          func.apply(this, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    }

    /**
     * Clean up resources and event listeners
     * @public
     */
    destroy() {
      try {
        // Clear timeouts and intervals
        this.timeouts.forEach(id => clearTimeout(id));
        this.intervals.forEach(id => clearInterval(id));

        // Abort any pending requests
        this.abortControllers.forEach(controller => {
          try {
            controller.abort();
          } catch (e) {
            // Ignore errors when aborting
          }
        });

        // Disconnect observers
        if (this.resizeObserver) {
          this.resizeObserver.disconnect();
        }
        if (this.intersectionObserver) {
          this.intersectionObserver.disconnect();
        }

        // Remove event listeners
        this.eventListeners.forEach((_, element) => {
          element.off();
        });

        // Clear caches
        this.requestCache.clear();

        // Clear references
        this.$container = null;
        this.$messages = null;
        this.$input = null;

        console.log("QiChat destroyed successfully");

      } catch (error) {
        console.error("Error during cleanup:", error);
      }
    }

    /**
     * Enhanced AJAX request with retry logic and caching
     * @param {Object} options - jQuery AJAX options
     * @param {number} [retryCount=0] - Current retry attempt
     * @returns {Promise} - Promise that resolves with the response
     */
    async makeRequest(options, retryCount = 0) {
      const cacheKey = JSON.stringify({
        url: options.url,
        data: options.data,
        type: options.type
      });

      // Check cache for GET requests and chat responses
      if ((options.type === 'GET' || this.isChatRequest(options)) && this.requestCache.has(cacheKey)) {
        const cached = this.requestCache.get(cacheKey);
        const cacheAge = Date.now() - cached.timestamp;
        const maxAge = this.isChatRequest(options) ? 60000 : 300000; // 1 min for chat, 5 min for others

        if (cacheAge < maxAge) {
          console.log('Returning cached response');
          return Promise.resolve(cached.data);
        }
      }

      // Create abort controller if supported
      let abortController;
      if (this.features.abortController) {
        abortController = new AbortController();
        this.abortControllers.add(abortController);
      }

      const startTime = Date.now();

      try {
        // Optimize timeout based on request type
        const optimizedTimeout = this.getOptimizedTimeout(options);

        const response = await $.ajax({
          ...options,
          timeout: optimizedTimeout,
          beforeSend: (xhr) => {
            if (abortController) {
              // Add abort signal to request
              xhr.abort = () => abortController.abort();
            }
            if (options.beforeSend) {
              options.beforeSend(xhr);
            }
          }
        });

        // Update performance metrics
        const responseTime = Date.now() - startTime;
        this.updatePerformanceMetrics(responseTime, true);

        // Cache successful requests with intelligent caching
        if (options.type === 'GET' || this.shouldCacheResponse(options, response)) {
          this.requestCache.set(cacheKey, {
            data: response,
            timestamp: Date.now()
          });

          // Limit cache size for performance
          if (this.requestCache.size > 50) {
            const oldestKey = this.requestCache.keys().next().value;
            this.requestCache.delete(oldestKey);
          }
        }

        return response;

      } catch (error) {
        this.updatePerformanceMetrics(Date.now() - startTime, false);

        // Check if we should retry
        if (retryCount < this.retryAttempts && this.shouldRetry(error)) {
          console.warn(`Request failed, retrying (${retryCount + 1}/${this.retryAttempts}):`, error);

          // Intelligent backoff based on error type
          const delay = this.getRetryDelay(error, retryCount);
          await this.sleep(delay);

          // Optimize retry request
          const optimizedOptions = this.optimizeRetryRequest(options, retryCount);
          return this.makeRequest(optimizedOptions, retryCount + 1);
        }

        throw error;
      } finally {
        if (abortController) {
          this.abortControllers.delete(abortController);
        }
      }
    }

    /**
     * Check if this is a chat request
     */
    isChatRequest(options) {
      return options.data && (
        options.data.action === 'qkb_chat_request' ||
        options.data.action === 'qkb_get_completion'
      );
    }

    /**
     * Get optimized timeout based on request type
     */
    getOptimizedTimeout(options) {
      if (this.isChatRequest(options)) {
        return 15000; // 15 seconds for chat requests
      }
      return options.timeout || 30000;
    }

    /**
     * Check if response should be cached
     */
    shouldCacheResponse(options, response) {
      // Cache successful chat responses for short periods
      if (this.isChatRequest(options) && response.success) {
        return true;
      }
      return options.type === 'GET';
    }

    /**
     * Get intelligent retry delay
     */
    getRetryDelay(error, retryCount) {
      // Faster retry for timeout errors
      if (error.statusText === 'timeout') {
        return this.retryDelay * (retryCount + 1);
      }
      // Exponential backoff for other errors
      return this.retryDelay * Math.pow(2, retryCount);
    }

    /**
     * Optimize request for retry
     */
    optimizeRetryRequest(options, retryCount) {
      const optimized = { ...options };

      // Reduce timeout for retries
      optimized.timeout = Math.max(5000, (optimized.timeout || 30000) - (retryCount * 5000));

      // For chat requests, try to reduce payload size
      if (this.isChatRequest(options) && optimized.data) {
        if (typeof optimized.data === 'object' && optimized.data.message) {
          // Truncate very long messages on retry
          if (optimized.data.message.length > 300) {
            optimized.data.message = optimized.data.message.substring(0, 300) + '...';
          }
        }
      }

      return optimized;
    }

    /**
     * Determine if a request should be retried
     * @param {Object} error - The error object
     * @returns {boolean} - Whether to retry the request
     */
    shouldRetry(error) {
      // Don't retry on user abort or client errors
      if (error.statusText === 'abort' ||
          (error.status >= 400 && error.status < 500)) {
        return false;
      }

      // Retry on network errors, timeouts, and server errors
      return error.status === 0 ||
             error.status >= 500 ||
             error.statusText === 'timeout';
    }

    /**
     * Sleep utility for retry delays
     * @param {number} ms - Milliseconds to sleep
     * @returns {Promise} - Promise that resolves after the delay
     */
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Update performance metrics
     * @param {number} responseTime - Response time in milliseconds
     * @param {boolean} success - Whether the request was successful
     */
    updatePerformanceMetrics(responseTime, success) {
      if (success) {
        this.performanceMetrics.messagesSent++;
        this.performanceMetrics.averageResponseTime =
          (this.performanceMetrics.averageResponseTime + responseTime) / 2;
      } else {
        this.performanceMetrics.errorCount++;
      }
      this.performanceMetrics.lastActivity = Date.now();
    }

    initializeModal() {
      const modalHtml = `
                <div class="qi-modal-overlay">
                    <div class="qi-modal">
                        <div class="qi-modal-header">
                            <h3 class="qi-modal-title"></h3>
                        </div>
                        <div class="qi-modal-content"></div>
                        <div class="qi-modal-actions">
                            <button class="qi-modal-button qi-cancel">Cancel</button>
                            <button class="qi-modal-button qi-confirm">Confirm</button>
                        </div>
                    </div>
                </div>
            `;
      this.$container.append(modalHtml);
      this.$modalOverlay = this.$container.find(".qi-modal-overlay");
      this.$modal = this.$container.find(".qi-modal");

      // Bind modal events
      this.$modal.find(".qi-cancel").on("click", () => this.hideModal());
      this.$modalOverlay.on("click", (e) => {
        if ($(e.target).hasClass("qi-modal-overlay")) {
          this.hideModal();
        }
      });
    }

    showModal(title, content, confirmCallback) {
      this.$modal.find(".qi-modal-title").text(title);
      this.$modal.find(".qi-modal-content").text(content);
      this.$modal
        .find(".qi-confirm")
        .off("click")
        .on("click", () => {
          confirmCallback();
          this.hideModal();
        });
      this.$modalOverlay.fadeIn(200);
    }

    hideModal() {
      this.$modalOverlay.fadeOut(200);
    }

    /**
     * Bind event handlers with proper cleanup tracking
     * @private
     */
    bindEvents() {
      try {
        // Core input events with debouncing for performance
        this.bindInputEvents();

        // UI control events
        this.bindUIControlEvents();

        // Chat management events
        this.bindChatManagementEvents();

        // Window and container events
        this.bindWindowEvents();

        // Delegated events for dynamic content
        this.bindDelegatedEvents();

        console.log("Event handlers bound successfully");

      } catch (error) {
        console.error("Failed to bind events:", error);
      }
    }

    /**
     * Bind input-related events
     * @private
     */
    bindInputEvents() {
      if (this.$input?.length) {
        // Debounced input handler for better performance
        const debouncedInputHandler = this.debounce(() => {
          this.handleInput();
          this.updateCharacterCount();
        }, 100);

        this.$input.on("input", debouncedInputHandler);
        this.$input.on("keydown", (e) => this.handleKeydown(e));
        this.$input.on("paste", (e) => this.handlePaste(e));

        // Track for cleanup
        this.eventListeners.set(this.$input, ['input', 'keydown', 'paste']);
      }
    }

    /**
     * Bind UI control events
     * @private
     */
    bindUIControlEvents() {
      // Send button
      if (this.$sendButton?.length) {
        this.$sendButton.on("click", () => this.sendMessage());
        this.eventListeners.set(this.$sendButton, ['click']);
      }

      // Fullscreen toggle
      if (this.$toggleFullscreenButton?.length) {
        this.$toggleFullscreenButton.on("click", () => this.toggleFullscreen());
        this.eventListeners.set(this.$toggleFullscreenButton, ['click']);
      }

      // Sidebar toggle
      if (this.$container.attr("data-show-sidebar") === "true" && this.$toggleSidebarButton?.length) {
        this.$toggleSidebarButton.on("click", () => this.toggleSidebar());
        this.eventListeners.set(this.$toggleSidebarButton, ['click']);
      }
    }

    /**
     * Bind chat management events
     * @private
     */
    bindChatManagementEvents() {
      // Assistant selection
      if (this.$assistantItems?.length) {
        this.$assistantItems.on("click", (e) => this.handleAssistantSelect(e));
        this.eventListeners.set(this.$assistantItems, ['click']);
      }

      // Chat controls
      if (this.$newChatButton?.length) {
        this.$newChatButton.on("click", () => this.startNewChat());
        this.eventListeners.set(this.$newChatButton, ['click']);
      }

      if (this.$clearHistoryButton?.length) {
        this.$clearHistoryButton.on("click", () => this.clearAllHistory());
        this.eventListeners.set(this.$clearHistoryButton, ['click']);
      }
    }

    /**
     * Bind window and container events
     * @private
     */
    bindWindowEvents() {
      // Throttled resize handler for better performance
      const throttledResizeHandler = this.throttle(() => this.handleResize(), 250);
      $(window).on("resize", throttledResizeHandler);

      // Keyboard shortcuts
      $(document).on("keydown", (e) => this.handleGlobalKeydown(e));

      // Track for cleanup
      this.eventListeners.set($(window), ['resize']);
      this.eventListeners.set($(document), ['keydown']);
    }

    /**
     * Bind delegated events for dynamic content
     * @private
     */
    bindDelegatedEvents() {
      if (!this.$container?.length) return;

      // History management
      this.$container.on("click", ".qi-history-item", (e) => {
        if (!$(e.target).closest(".qi-delete-history").length) {
          this.loadHistoryItem(e);
        }
      });

      this.$container.on("click", ".qi-delete-history", (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.handleHistoryItemDelete(e);
      });

      // Assistant selection
      this.$container.on("change", ".qi-assistant-select", (e) => {
        this.setCurrentAssistant($(e.currentTarget).val());
      });

      // Export functionality
      this.$container.on("click", ".qi-export-button", (e) => {
        e.preventDefault();
        this.showExportModal();
      });

      // Message actions
      this.$container.on("click", ".qi-copy-button", (e) => {
        e.preventDefault();
        e.stopPropagation();
        const $message = $(e.target).closest(".qi-message");
        this.copyMessageToClipboard($message);
      });

      // Code block copy buttons
      this.$container.on("click", ".qi-copy-code-btn", (e) => {
        e.preventDefault();
        e.stopPropagation();
        const $codeBlock = $(e.target).closest(".qi-code-block");
        const code = $codeBlock.find("code").text();
        this.copyTextToClipboard(code).then(() => {
          const $button = $(e.target).closest(".qi-copy-code-btn");
          const originalHtml = $button.html();
          $button.html('<i class="fas fa-check"></i>');
          setTimeout(() => {
            $button.html(originalHtml);
          }, 2000);
        }).catch(() => {
          this.showError("Failed to copy code to clipboard");
        });
      });

      // Feedback buttons
      this.$container.on("click", ".qi-feedback-button", (e) => {
        e.preventDefault();
        e.stopPropagation();
        const $button = $(e.currentTarget);
        const messageId = $button.closest(".qi-message").data("message-id");
        const feedbackType = $button.data("feedback");
        this.submitFeedback(messageId, feedbackType);
      });
    }

    /**
     * Handle paste events with file support
     * @private
     * @param {Event} e - Paste event
     */
    handlePaste(e) {
      const clipboardData = e.originalEvent.clipboardData;
      if (clipboardData?.files?.length > 0) {
        e.preventDefault();
        const file = clipboardData.files[0];
        this.handleFileSelect(file);
      }
    }

    /**
     * Handle global keyboard shortcuts
     * @private
     * @param {Event} e - Keydown event
     */
    handleGlobalKeydown(e) {
      // Only handle shortcuts when chat is focused
      if (!this.$container?.is(':visible')) return;

      // Escape key to close modals
      if (e.key === 'Escape') {
        this.hideModal();
        this.hideExportModal();
      }

      // Ctrl/Cmd + Enter to send message
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        if (this.$input?.is(':focus') && !this.$sendButton?.prop('disabled')) {
          this.sendMessage();
        }
      }
    }

    handleInput() {
      const $textarea = this.$input;
      const value = $textarea.val();
      this.$sendButton.prop("disabled", !value.trim());

      // Reset height to auto to properly calculate scrollHeight
      $textarea.css("height", "auto");

      // Calculate new height while respecting max-height
      const newHeight = Math.min(
        Math.max($textarea[0].scrollHeight, 24), // minimum 24px
        180 // maximum height (container max-height - padding)
      );

      // Apply new height with smooth transition
      $textarea.css({
        height: `${newHeight}px`,
        "overflow-y": newHeight === 180 ? "auto" : "hidden",
      });

      // Show scrollbar only when needed
      if (newHeight === 180 && $textarea[0].scrollHeight > 180) {
        $textarea.css("overflow-y", "scroll");
      }
    }

    handleKeydown(e) {
      if (e.key === "Enter") {
        if (e.shiftKey) {
          // Check if we're at max height before adding new line
          const $textarea = this.$input;
          const atMaxHeight = $textarea[0].scrollHeight > 180;

          if (atMaxHeight) {
            // Only allow new line if content is scrollable
            if (
              $textarea[0].scrollTop + $textarea.height() <
              $textarea[0].scrollHeight
            ) {
              return;
            }
            // Prevent new line if we're at max height and bottom
            if (!$textarea.val().endsWith("\n")) {
              e.preventDefault();
            }
          }
          return; // Allow new line with Shift+Enter if not at limits
        }

        e.preventDefault();
        if (!this.$sendButton.prop("disabled")) {
          this.sendMessage();
        }
      }
    }

    /**
     * Send a message to the assistant with enhanced error handling and rate limiting
     * @param {string} [messageText] - The message text (if not provided, will use input value)
     * @param {boolean} [isEdited=false] - Whether this is an edited message
     * @param {jQuery} [$existingMessage=null] - The existing message element to update (for edited messages)
     * @param {string} [originalContent=''] - The original content of the message (for restoring on error)
     */
    async sendMessage(
      messageText,
      isEdited = false,
      $existingMessage = null,
      originalContent = ""
    ) {
      // If messageText is not provided, get it from the input
      const message = messageText || this.$input.val().trim();
      if (!message || this.isTyping) return;

      // Check rate limiting for new messages
      if (!isEdited && !this.checkRateLimit('sendMessage', 20)) {
        this.showError('You are sending messages too quickly. Please wait a moment before trying again.');
        return;
      }

      // Implement request debouncing for performance
      if (!isEdited) {
        const now = Date.now();
        if (now - this.lastRequestTime < this.minRequestInterval) {
          const waitTime = this.minRequestInterval - (now - this.lastRequestTime);
          console.log(`Debouncing request, waiting ${waitTime}ms`);
          await this.sleep(waitTime);
        }

        // Cancel any pending request
        if (this.pendingRequest) {
          console.log('Cancelling previous request');
          if (this.pendingRequest.abort) {
            this.pendingRequest.abort();
          }
          this.pendingRequest = null;
        }

        this.lastRequestTime = Date.now();
      }

      // Sanitize the message input
      const sanitizedMessage = this.sanitizeInput(message);
      if (!sanitizedMessage.trim()) {
        this.showError('Message contains invalid content. Please try again.');
        return;
      }

      // Check if there's a file attached
      const $filePreview = $(".qi-file-preview");
      let hasAttachment = $filePreview.length > 0;
      let fileData = null;

      if (hasAttachment) {
        // Get the file from the input
        const fileInput = document.getElementById("qi-file-input");
        if (fileInput && fileInput.files && fileInput.files.length > 0) {
          fileData = fileInput.files[0];
        }
      }

      try {
        this.isTyping = true;

        // Only disable input if this is a new message, not an edited one
        if (!isEdited) {
          this.$input.prop("disabled", true);
          this.$sendButton.prop("disabled", true);

          // Remove welcome message if it exists
          this.$messages.find(".qi-welcome-message").remove();

          // Add class to qi-chat-wrapper to adjust layout after first message
          this.$container.find(".qi-chat-wrapper").addClass("has-messages");

          // Create message content with file attachment if present
          let messageContent = message;
          if (hasAttachment && fileData) {
            // Add file info to the message
            const fileIcon = this.getFileIcon(fileData.type);
            messageContent = `<div class="qi-message-file-attachment">
                            <div class="qi-message-file-icon"><i class="fas ${fileIcon}"></i></div>
                            <div class="qi-message-file-info">
                                <div class="qi-message-file-name">${
                                  fileData.name
                                }</div>
                                <div class="qi-message-file-size">${this.formatFileSize(
                                  fileData.size
                                )}</div>
                            </div>
                        </div>
                        <div class="qi-message-text">${message}</div>`;

            // Remove the file preview and clear the file input
            $(".qi-file-preview").remove();
            $("#qi-file-input").val("");
          }

          // Add user message
          this.addMessage(messageContent, "user");
          this.$input.val("");
          this.$input.trigger("input");

          // Show typing indicator
          this.showTypingIndicator();
        }

        const data = {
          action: "qkb_chat_request",
          nonce: qkbChatbot.nonce,
          message: sanitizedMessage, // Use sanitized message
          assistant_id: this.currentAssistant,
          has_attachment: hasAttachment ? 1 : 0,
          // Add debug info
          debug_info: {
            assistant_type: typeof this.currentAssistant,
            assistant_value: this.currentAssistant,
          },
        };

        // If there's a file attachment, add it to the message data
        let response;
        if (hasAttachment && fileData) {
          // Create FormData to handle file upload
          const formData = new FormData();

          // Add all the regular data fields
          for (const key in data) {
            if (typeof data[key] === "object") {
              formData.append(key, JSON.stringify(data[key]));
            } else {
              formData.append(key, data[key]);
            }
          }

          // Add the file
          formData.append("file", fileData);

          // Store reference to pending request for file uploads
          const requestPromise = this.makeRequest({
            url: qkbChatbot.ajax_url,
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
          });

          this.pendingRequest = { abort: () => {} }; // FormData requests handled by makeRequest
          response = await requestPromise;
        } else {
          // Store reference to pending request for regular requests
          const requestPromise = this.makeRequest({
            url: qkbChatbot.ajax_url,
            type: "POST",
            data: data,
          });

          this.pendingRequest = { abort: () => {} }; // Handled by makeRequest's abort controller
          response = await requestPromise;
        }

        // Clear pending request
        this.pendingRequest = null;

        // Hide typing indicator if not editing
        if (!isEdited) {
          this.hideTypingIndicator();
        }

        if (response.success && response.data) {
          if (isEdited && $existingMessage) {
            // Update the existing message with the new response
            $existingMessage
              .find(".qi-message-content")
              .html(this.formatMessage(response.data.message));
            // Remove the updating class
            $existingMessage.removeClass("qi-updating");
          } else {
            // Add a new assistant message
            this.addMessage(response.data.message, "assistant");
          }

          // Update chat history if we're continuing a chat
          if (this.currentChatId) {
            this.updateChatHistory();
          }
        } else {
          if (isEdited && $existingMessage && originalContent) {
            // Restore the original content if there was an error
            $existingMessage.find(".qi-message-content").html(originalContent);
            // Remove the updating class
            $existingMessage.removeClass("qi-updating");
          }
          this.showError(response.data || "An error occurred");
        }
      } catch (error) {
        console.error("Chat Error:", error);
        this.hideTypingIndicator();

        if (isEdited && $existingMessage && originalContent) {
          // Restore the original content if there was an error
          $existingMessage.find(".qi-message-content").html(originalContent);
          // Remove the updating class
          $existingMessage.removeClass("qi-updating");
          this.showError("Failed to update the response. Please try again.");
        } else {
          this.showError(
            "I apologize, but I encountered an error processing your request. Please try again."
          );
        }
      } finally {
        this.isTyping = false;

        // Only re-enable input if this is a new message, not an edited one
        if (!isEdited) {
          this.$input.prop("disabled", false);
          this.$sendButton.prop("disabled", false);
          this.$input.focus();
        }
      }
    }

    addMessage(content, type) {
      try {
        // Use cached elements for better performance
        if (!this.$chatWrapper.hasClass("has-messages")) {
          this.$chatWrapper.addClass("has-messages");
          this.$messages.find(".qi-welcome-message").hide();
        }

        const messageId = Date.now();

        // Use document fragment for better performance
        const fragment = document.createDocumentFragment();
        const messageElement = document.createElement('div');
        messageElement.className = `qi-message qi-${type}-message`;
        messageElement.setAttribute('data-message-id', messageId);

        const contentElement = document.createElement('div');
        contentElement.className = 'qi-message-content';

        messageElement.appendChild(contentElement);
        fragment.appendChild(messageElement);

        const $message = $(messageElement);
        const $content = $(contentElement);

        // Adjust message container height for responsive layout
        this.adjustMessageContainerHeight();

        // Create message actions container
        const $messageActions = $("<div>", {
          class: "qi-message-actions",
        });

        if (type === "assistant") {
          // Copy button for assistant messages
          const $copyButton = $("<button>", {
            class: "qi-action-button qi-copy-button",
            html: '<i class="fas fa-copy"></i>',
            title: "Copy to clipboard",
          }).on("click", (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.copyMessageToClipboard($message);
          });

          // Feedback buttons
          const $feedbackContainer = $("<div>", {
            class: "qi-feedback-container",
          });

          const $thumbsUpButton = $("<button>", {
            class: "qi-feedback-button qi-thumbs-up",
            html: '<i class="fas fa-thumbs-up"></i>',
            title: "This was helpful",
            "data-feedback": "positive",
          }).on("click", (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.submitFeedback(messageId, "positive");
          });

          const $thumbsDownButton = $("<button>", {
            class: "qi-feedback-button qi-thumbs-down",
            html: '<i class="fas fa-thumbs-down"></i>',
            title: "This was not helpful",
            "data-feedback": "negative",
          }).on("click", (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.submitFeedback(messageId, "negative");
          });

          $feedbackContainer.append($thumbsUpButton, $thumbsDownButton);
          $messageActions.append($copyButton, $feedbackContainer);
          $message.append($messageActions);
        } else if (type === "user") {
          // User message actions have been removed
          $message.append($messageActions);
        }

        this.$messages.append($message);

        // Set up virtual scrolling observer for new message
        if (this.intersectionObserver) {
          this.intersectionObserver.observe($message[0]);
        }

        // Add accessibility attributes
        $message.attr('role', 'article');
        $message.attr('aria-label', `${type} message`);

        this.scrollToBottom();

        // Typing effect
        if (type === "assistant") {
          this.simulateTypingEffect($content, content, () => {
            // After typing is complete, add to history
            if (this.currentAssistant) {
              if (!this.chatHistory[this.currentAssistant]) {
                this.chatHistory[this.currentAssistant] = [];
              }
              this.chatHistory[this.currentAssistant].push({
                type,
                content,
                timestamp: messageId,
                assistantId: this.currentAssistant,
              });
              this.saveChatHistory();
            }

            // Apply syntax highlighting to code blocks
            this.applySyntaxHighlighting($message);

            // Show message actions after typing is complete
            $message.find(".qi-message-actions").addClass("visible");

            // Trigger accessibility event
            this.trigger('messageAdded', $message);
          });
        } else {
          $content.html(this.formatMessage(content));
          this.applySyntaxHighlighting($content);
          // Add to history immediately for user messages
          if (this.currentAssistant) {
            if (!this.chatHistory[this.currentAssistant]) {
              this.chatHistory[this.currentAssistant] = [];
            }
            this.chatHistory[this.currentAssistant].push({
              type,
              content,
              timestamp: messageId,
              assistantId: this.currentAssistant,
            });
            this.saveChatHistory();
          }

          // Trigger accessibility event for user messages too
          this.trigger('messageAdded', $message);
        }
      } catch (error) {
        console.error("Error adding message:", error);
      }
    }

    simulateTypingEffect($element, text, callback) {
      // Split the text into tokens that preserve whitespace and line breaks
      const tokens = text.split(/(\s+)/);
      let currentIndex = 0;
      let currentText = "";

      this.isTyping = true;
      const typingInterval = setInterval(() => {
        if (currentIndex < tokens.length) {
          // Add the next token (word or whitespace)
          currentText += tokens[currentIndex];

          $element.html(this.formatMessage(currentText));
          this.applySyntaxHighlighting($element);
          this.scrollToBottom();
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          this.isTyping = false;
          this.applySyntaxHighlighting($element);
          if (callback) {
            callback();
          }
        }
      }, 50); // Slightly slower speed for word-by-word typing for better readability
    }

    /**
     * Sanitize and format message content with XSS prevention
     * @param {string} content - Raw message content
     * @returns {string} - Sanitized and formatted HTML
     */
    formatMessage(content) {
      try {
        if (!content) return "";

        // First sanitize the content to prevent XSS
        content = this.sanitizeInput(content);

        // Store code blocks temporarily to prevent interference
        const codeBlocks = [];
        let codeBlockIndex = 0;

        // Enhanced code block handling with better language detection
        content = content.replace(
          /```(\w+)?\s*\n?([\s\S]*?)```/g,
          (match, language, code) => {
            let lang = language ? language.toLowerCase() : 'text';

            // Auto-detect language if not specified
            if (!language && code.trim()) {
              lang = this.detectCodeLanguage(code.trim());
            }

            const escapedCode = this.escapeHtml(code.trim());
            const placeholder = `__CODE_BLOCK_${codeBlockIndex}__`;
            const displayLang = this.getLanguageDisplayName(lang);

            codeBlocks[codeBlockIndex] = `<div class="qi-code-block">
              <div class="qi-code-header">
                <span class="qi-code-language">${displayLang}</span>
                <button class="qi-copy-code-btn" title="Copy code">
                  <i class="fas fa-copy"></i>
                </button>
              </div>
              <pre><code class="language-${lang}">${escapedCode}</code></pre>
            </div>`;
            codeBlockIndex++;
            return placeholder;
          }
        );

        // Handle inline code (protect from other processing)
        const inlineCodes = [];
        let inlineCodeIndex = 0;
        content = content.replace(/`([^`]+)`/g, (match, code) => {
          const placeholder = `__INLINE_CODE_${inlineCodeIndex}__`;
          inlineCodes[inlineCodeIndex] = `<code class="qi-inline-code">${this.escapeHtml(code)}</code>`;
          inlineCodeIndex++;
          return placeholder;
        });

        // Handle tables first (before other processing)
        content = this.parseMarkdownTables(content);
        content = this.parsePipeSeparatedTables(content);

        // Handle headings with improved spacing
        content = content.replace(/^#### (.+)$/gm, "<h4 class=\"qi-message-heading qi-h4\">$1</h4>");
        content = content.replace(/^### (.+)$/gm, "<h3 class=\"qi-message-heading qi-h3\">$1</h3>");
        content = content.replace(/^## (.+)$/gm, "<h2 class=\"qi-message-heading qi-h2\">$1</h2>");
        content = content.replace(/^# (.+)$/gm, "<h1 class=\"qi-message-heading qi-h1\">$1</h1>");

        // Handle blockquotes with multi-line support
        content = this.parseBlockquotes(content);

        // Enhanced list handling with nested support
        content = this.parseNestedLists(content);

        // Enhanced text formatting with additional styles
        content = content.replace(/\*\*\*([^*]+)\*\*\*/g, "<strong><em>$1</em></strong>"); // Bold italic
        content = content.replace(/\*\*([^*]+)\*\*/g, "<strong>$1</strong>"); // Bold
        content = content.replace(/\*([^*]+)\*/g, "<em>$1</em>"); // Italic
        content = content.replace(/~~([^~]+)~~/g, "<del>$1</del>"); // Strikethrough
        content = content.replace(/\^([^\s^]+)/g, "<sup>$1</sup>"); // Superscript
        content = content.replace(/~([^\s~]+)/g, "<sub>$1</sub>"); // Subscript

        // Handle links with improved validation
        content = content.replace(
          /\[([^\]]+)\]\(([^)]+)\)/g,
          (match, text, url) => {
            const sanitizedUrl = this.sanitizeUrl(url);
            const sanitizedText = this.escapeHtml(text);
            return sanitizedUrl ?
              `<a href="${sanitizedUrl}" target="_blank" rel="noopener noreferrer" class="qi-message-link">${sanitizedText}</a>` :
              sanitizedText;
          }
        );

        // Enhanced paragraph and line break processing
        content = this.processContentBlocks(content);

        // Restore code blocks
        codeBlocks.forEach((codeBlock, index) => {
          content = content.replace(`__CODE_BLOCK_${index}__`, codeBlock);
        });

        // Restore inline codes
        inlineCodes.forEach((inlineCode, index) => {
          content = content.replace(`__INLINE_CODE_${index}__`, inlineCode);
        });

        // Final cleanup and validation
        content = this.finalizeContent(content);

        return content;
      } catch (error) {
        console.error("Error formatting message:", error);
        return `<p class="qi-message-paragraph">${this.escapeHtml(content)}</p>`;
      }
    }

    /**
     * Detect programming language from code content
     * @param {string} code - Code content to analyze
     * @returns {string} - Detected language
     */
    detectCodeLanguage(code) {
      const patterns = {
        javascript: [/function\s+\w+/, /const\s+\w+\s*=/, /let\s+\w+\s*=/, /var\s+\w+\s*=/, /=>\s*{/, /console\.log/],
        python: [/def\s+\w+/, /import\s+\w+/, /from\s+\w+\s+import/, /print\s*\(/, /if\s+__name__\s*==/, /class\s+\w+:/],
        php: [/<\?php/, /\$\w+\s*=/, /function\s+\w+/, /echo\s+/, /->/, /namespace\s+/],
        java: [/public\s+class/, /public\s+static\s+void\s+main/, /System\.out\.println/, /import\s+java\./, /private\s+\w+/, /public\s+\w+/],
        css: [/\w+\s*{/, /:\s*\w+;/, /@media/, /\.[\w-]+\s*{/, /#[\w-]+\s*{/],
        html: [/<html/, /<div/, /<span/, /<p>/, /<h[1-6]>/, /<!DOCTYPE/],
        sql: [/SELECT\s+/, /FROM\s+/, /WHERE\s+/, /INSERT\s+INTO/, /UPDATE\s+/, /DELETE\s+FROM/],
        json: [/^\s*{/, /^\s*\[/, /"[\w-]+"\s*:/, /}\s*,?\s*$/],
        xml: [/<\?xml/, /<\/\w+>/, /<\w+[^>]*>/],
        bash: [/^#!/, /echo\s+/, /if\s*\[/, /for\s+\w+\s+in/, /grep\s+/, /awk\s+/],
        yaml: [/^\s*\w+:\s*$/, /^\s*-\s+/, /---/, /\.\.\./]
      };

      for (const [lang, regexes] of Object.entries(patterns)) {
        if (regexes.some(regex => regex.test(code))) {
          return lang;
        }
      }

      return 'text';
    }

    /**
     * Get display name for programming language
     * @param {string} lang - Language code
     * @returns {string} - Display name
     */
    getLanguageDisplayName(lang) {
      const displayNames = {
        javascript: 'JavaScript',
        python: 'Python',
        php: 'PHP',
        java: 'Java',
        css: 'CSS',
        html: 'HTML',
        sql: 'SQL',
        json: 'JSON',
        xml: 'XML',
        bash: 'Bash',
        yaml: 'YAML',
        text: 'Text'
      };

      return displayNames[lang] || lang.charAt(0).toUpperCase() + lang.slice(1);
    }

    /**
     * Parse blockquotes with multi-line support
     * @param {string} content - Content with potential blockquotes
     * @returns {string} - Content with HTML blockquotes
     */
    parseBlockquotes(content) {
      const lines = content.split('\n');
      const processedLines = [];
      let inBlockquote = false;
      let blockquoteLines = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();

        if (trimmedLine.startsWith('> ')) {
          if (!inBlockquote) {
            inBlockquote = true;
            blockquoteLines = [];
          }
          blockquoteLines.push(trimmedLine.substring(2));
        } else {
          if (inBlockquote) {
            // End the blockquote
            const blockquoteContent = blockquoteLines.join('<br>');
            processedLines.push(`<blockquote class="qi-message-quote">${blockquoteContent}</blockquote>`);
            inBlockquote = false;
            blockquoteLines = [];
          }
          processedLines.push(line);
        }
      }

      // Handle any remaining blockquote at the end
      if (inBlockquote && blockquoteLines.length > 0) {
        const blockquoteContent = blockquoteLines.join('<br>');
        processedLines.push(`<blockquote class="qi-message-quote">${blockquoteContent}</blockquote>`);
      }

      return processedLines.join('\n');
    }

    /**
     * Parse nested lists with improved handling
     * @param {string} content - Content with potential lists
     * @returns {string} - Content with HTML lists
     */
    parseNestedLists(content) {
      const lines = content.split('\n');
      const processedLines = [];
      let listStack = [];
      let currentIndent = 0;
      let inList = false;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();
        const indent = line.length - line.trimStart().length;

        // Check for list items
        const unorderedMatch = trimmedLine.match(/^[-*+]\s+(.+)$/);
        const orderedMatch = trimmedLine.match(/^\d+\.\s+(.+)$/);

        if (unorderedMatch || orderedMatch) {
          const itemContent = unorderedMatch ? unorderedMatch[1] : orderedMatch[1];
          const listType = unorderedMatch ? 'ul' : 'ol';

          if (!inList) {
            // Start first list
            inList = true;
            listStack.push({ type: listType, indent: indent });
            const listClass = listType === 'ul' ? 'qi-message-list' : 'qi-message-list qi-ordered-list';
            processedLines.push(`<${listType} class="${listClass}">`);
            currentIndent = indent;
          } else if (indent > currentIndent) {
            // Start new nested list
            listStack.push({ type: listType, indent: indent });
            const listClass = listType === 'ul' ? 'qi-message-list' : 'qi-message-list qi-ordered-list';
            processedLines.push(`<${listType} class="${listClass}">`);
            currentIndent = indent;
          } else if (indent < currentIndent) {
            // Close nested lists
            while (listStack.length > 0 && listStack[listStack.length - 1].indent > indent) {
              const closingList = listStack.pop();
              processedLines.push(`</${closingList.type}>`);
            }
            currentIndent = indent;
          }

          processedLines.push(`<li class="qi-list-item">${itemContent}</li>`);
        } else {
          // Not a list item
          if (inList) {
            // Check if this is just an empty line within a list context
            if (trimmedLine === '' && i < lines.length - 1) {
              // Look ahead to see if the next non-empty line is a list item
              let nextListItem = false;
              for (let j = i + 1; j < lines.length; j++) {
                const nextLine = lines[j].trim();
                if (nextLine === '') continue;
                if (nextLine.match(/^[-*+]\s+/) || nextLine.match(/^\d+\.\s+/)) {
                  nextListItem = true;
                }
                break;
              }

              if (nextListItem) {
                // Skip empty line within list
                continue;
              }
            }

            // Close all open lists
            while (listStack.length > 0) {
              const closingList = listStack.pop();
              processedLines.push(`</${closingList.type}>`);
            }
            inList = false;
            currentIndent = 0;
          }

          // Only add non-empty lines or preserve intentional spacing
          if (trimmedLine !== '' || !inList) {
            processedLines.push(line);
          }
        }
      }

      // Close any remaining open lists
      while (listStack.length > 0) {
        const closingList = listStack.pop();
        processedLines.push(`</${closingList.type}>`);
      }

      return processedLines.join('\n');
    }

    /**
     * Parse markdown-style tables
     * @param {string} content - Content with potential tables
     * @returns {string} - Content with HTML tables
     */
    parseMarkdownTables(content) {
      try {
        // Enhanced regex to match various table formats
        // Standard markdown table with header separator
        const standardTableRegex = /^(\|.*\|)\s*\n(\|[-\s|:]+\|)\s*\n((?:\|.*\|\s*\n?)*)/gm;

        // Simple pipe-separated table without header separator
        const simpleTableRegex = /^(\|[^|\n]+\|[^|\n]*\|.*)\n((?:\|[^|\n]+\|[^|\n]*\|.*\n?)*)/gm;

        // First try standard markdown tables
        content = content.replace(standardTableRegex, (match, headerRow, separatorRow, bodyRows) => {
          return this.buildHtmlTable(headerRow, bodyRows, true);
        });

        // Then try simple pipe-separated tables
        content = content.replace(simpleTableRegex, (match, firstRow, remainingRows) => {
          // Check if this looks like a table (has multiple pipe-separated rows)
          const allRows = (firstRow + '\n' + remainingRows).trim();
          const rows = allRows.split('\n').filter(row => row.includes('|'));

          if (rows.length >= 2) {
            // Treat first row as header
            return this.buildHtmlTable(rows[0], rows.slice(1).join('\n'), false);
          }
          return match;
        });

        return content;
      } catch (error) {
        console.error('Error parsing tables:', error);
        return content;
      }
    }

    /**
     * Parse pipe-separated tables (like the format in your screenshot)
     * @param {string} content - Content with potential pipe-separated tables
     * @returns {string} - Content with HTML tables
     */
    parsePipeSeparatedTables(content) {
      try {
        // Look for patterns like "Feature | Description |" followed by multiple similar lines
        const lines = content.split('\n');
        const tableBlocks = [];
        let currentBlock = [];
        let inTable = false;

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();

          // Check if line looks like a table row (has at least 2 pipes and some content)
          const pipeCount = (line.match(/\|/g) || []).length;
          const hasContent = line.length > 3 && pipeCount >= 2;

          if (hasContent && line.includes('|')) {
            if (!inTable) {
              inTable = true;
              currentBlock = [];
            }
            currentBlock.push(line);
          } else {
            if (inTable && currentBlock.length >= 2) {
              // We have a complete table block
              tableBlocks.push({
                startIndex: i - currentBlock.length,
                endIndex: i - 1,
                lines: [...currentBlock]
              });
            }
            inTable = false;
            currentBlock = [];
          }
        }

        // Handle last block if it's a table
        if (inTable && currentBlock.length >= 2) {
          tableBlocks.push({
            startIndex: lines.length - currentBlock.length,
            endIndex: lines.length - 1,
            lines: [...currentBlock]
          });
        }

        // Replace table blocks with HTML tables (in reverse order to maintain indices)
        for (let i = tableBlocks.length - 1; i >= 0; i--) {
          const block = tableBlocks[i];
          const tableHtml = this.buildHtmlTableFromLines(block.lines);

          // Replace the lines with the HTML table
          lines.splice(block.startIndex, block.lines.length, tableHtml);
        }

        return lines.join('\n');
      } catch (error) {
        console.error('Error parsing pipe-separated tables:', error);
        return content;
      }
    }

    /**
     * Process content blocks for better paragraph and line break handling
     * @param {string} content - Content to process
     * @returns {string} - Processed content
     */
    processContentBlocks(content) {
      try {
        // Split content into blocks separated by double line breaks
        const blocks = content.split(/\n\s*\n/);
        const processedBlocks = [];

        blocks.forEach(block => {
          block = block.trim();
          if (!block) return;

          // Check if block is already a formatted element
          if (block.match(/^<(h[1-6]|ul|ol|blockquote|div|table|pre)/)) {
            processedBlocks.push(block);
          } else {
            // Handle single line breaks more carefully
            // Don't convert all line breaks to <br> - only when appropriate
            const lines = block.split('\n');

            if (lines.length === 1) {
              // Single line - wrap in paragraph
              processedBlocks.push(`<p class="qi-message-paragraph">${block}</p>`);
            } else {
              // Multiple lines - check if they should be separate paragraphs or line breaks
              const shouldBeParagraphs = lines.some(line =>
                line.trim().length > 50 || // Long lines are likely separate thoughts
                line.trim().match(/^[A-Z]/) // Lines starting with capital letters
              );

              if (shouldBeParagraphs) {
                // Treat as separate paragraphs
                lines.forEach(line => {
                  line = line.trim();
                  if (line) {
                    processedBlocks.push(`<p class="qi-message-paragraph">${line}</p>`);
                  }
                });
              } else {
                // Treat as line breaks within a paragraph
                const formattedBlock = block.replace(/\n/g, '<br>');
                processedBlocks.push(`<p class="qi-message-paragraph">${formattedBlock}</p>`);
              }
            }
          }
        });

        return processedBlocks.join('\n');
      } catch (error) {
        console.error('Error processing content blocks:', error);
        return content;
      }
    }

    /**
     * Finalize content with cleanup and validation
     * @param {string} content - Content to finalize
     * @returns {string} - Finalized content
     */
    finalizeContent(content) {
      try {
        // Clean up empty paragraphs
        content = content.replace(/<p class="qi-message-paragraph">\s*<\/p>/g, '');

        // Limit consecutive line breaks
        content = content.replace(/(<br>\s*){3,}/g, '<br><br>');

        // Clean up extra whitespace around block elements
        content = content.replace(/\s*(<\/?(h[1-6]|ul|ol|blockquote|div|table|pre)[^>]*>)\s*/g, '$1');

        // Ensure proper spacing between block elements
        content = content.replace(/(<\/(h[1-6]|ul|ol|blockquote|div|table|pre)>)(<(h[1-6]|ul|ol|blockquote|div|table|pre|p)[^>]*>)/g, '$1\n\n$2');

        // Clean up <br> tags that appear right after opening paragraph tags or before closing ones
        content = content.replace(/<p([^>]*)>\s*<br>/g, '<p$1>');
        content = content.replace(/<br>\s*<\/p>/g, '</p>');

        // Ensure we have content wrapped properly
        if (content && !content.match(/^<(div|h[1-6]|ul|ol|blockquote|pre|table|p)/)) {
          content = `<p class="qi-message-paragraph">${content}</p>`;
        }

        return content;
      } catch (error) {
        console.error('Error finalizing content:', error);
        return content;
      }
    }

    /**
     * Build HTML table from array of pipe-separated lines
     * @param {Array} lines - Array of pipe-separated lines
     * @returns {string} - HTML table
     */
    buildHtmlTableFromLines(lines) {
      try {
        if (lines.length === 0) return '';

        // Parse all rows
        const allRows = lines.map(line =>
          line.split('|')
            .map(cell => cell.trim())
            .filter(cell => cell !== '')
        ).filter(row => row.length > 0);

        if (allRows.length === 0) return lines.join('\n');

        // Build HTML table
        let tableHtml = '<div class="qi-table-container"><table class="qi-message-table">';

        // Use first row as header
        const headers = allRows[0];
        if (headers.length > 0) {
          tableHtml += '<thead><tr>';
          headers.forEach(header => {
            tableHtml += `<th class="qi-table-header">${this.escapeHtml(header)}</th>`;
          });
          tableHtml += '</tr></thead>';
        }

        // Add remaining rows as body
        const bodyRows = allRows.slice(1);
        if (bodyRows.length > 0) {
          tableHtml += '<tbody>';
          bodyRows.forEach(row => {
            tableHtml += '<tr class="qi-table-row">';
            row.forEach(cell => {
              tableHtml += `<td class="qi-table-cell">${this.escapeHtml(cell)}</td>`;
            });
            tableHtml += '</tr>';
          });
          tableHtml += '</tbody>';
        }

        tableHtml += '</table></div>';
        return tableHtml;
      } catch (error) {
        console.error('Error building HTML table from lines:', error);
        return lines.join('\n');
      }
    }

    /**
     * Build HTML table from parsed rows
     * @param {string} headerRow - Header row string
     * @param {string} bodyRows - Body rows string
     * @param {boolean} hasHeaderSeparator - Whether there's a header separator
     * @returns {string} - HTML table
     */
    buildHtmlTable(headerRow, bodyRows, hasHeaderSeparator = false) {
      try {
        // Parse header
        const headers = headerRow.split('|')
          .map(cell => cell.trim())
          .filter(cell => cell !== '');

        // Parse body rows
        const rows = bodyRows.trim().split('\n')
          .map(row => row.split('|')
            .map(cell => cell.trim())
            .filter(cell => cell !== ''))
          .filter(row => row.length > 0);

        // Build HTML table
        let tableHtml = '<div class="qi-table-container"><table class="qi-message-table">';

        // Add header
        if (headers.length > 0) {
          tableHtml += '<thead><tr>';
          headers.forEach(header => {
            tableHtml += `<th class="qi-table-header">${this.escapeHtml(header)}</th>`;
          });
          tableHtml += '</tr></thead>';
        }

        // Add body
        if (rows.length > 0) {
          tableHtml += '<tbody>';
          rows.forEach(row => {
            tableHtml += '<tr class="qi-table-row">';
            row.forEach(cell => {
              tableHtml += `<td class="qi-table-cell">${this.escapeHtml(cell)}</td>`;
            });
            tableHtml += '</tr>';
          });
          tableHtml += '</tbody>';
        }

        tableHtml += '</table></div>';
        return tableHtml;
      } catch (error) {
        console.error('Error building HTML table:', error);
        return headerRow + '\n' + bodyRows;
      }
    }

    /**
     * Apply syntax highlighting to code blocks using Prism.js
     * @param {jQuery} $element - Element containing code blocks
     */
    applySyntaxHighlighting($element) {
      try {
        // Check if Prism is available
        if (typeof window.Prism !== 'undefined') {
          // Find all code blocks and apply highlighting
          $element.find('code[class*="language-"]').each(function() {
            const $code = $(this);
            const codeElement = $code[0];

            // Apply Prism highlighting
            window.Prism.highlightElement(codeElement);
          });
        } else {
          // Fallback: add a class for basic styling
          $element.find('code[class*="language-"]').addClass('qi-code-fallback');
        }

        // Add copy functionality to code blocks
        this.addCopyCodeButtons($element);
      } catch (error) {
        console.error('Error applying syntax highlighting:', error);
      }
    }

    /**
     * Add copy buttons to code blocks
     * @param {jQuery} $element - Element containing code blocks
     */
    addCopyCodeButtons($element) {
      try {
        $element.find('.qi-copy-code-btn').off('click').on('click', (e) => {
          e.preventDefault();
          const $button = $(e.currentTarget);
          const $codeBlock = $button.closest('.qi-code-block');
          const $code = $codeBlock.find('code');
          const codeText = $code.text();

          this.copyTextToClipboard(codeText).then(() => {
            // Show success feedback
            const originalHtml = $button.html();
            $button.html('<i class="fas fa-check"></i>');
            $button.addClass('qi-copy-success');

            setTimeout(() => {
              $button.html(originalHtml);
              $button.removeClass('qi-copy-success');
            }, 2000);
          }).catch((error) => {
            console.error('Failed to copy code:', error);
            this.showError('Failed to copy code to clipboard');
          });
        });
      } catch (error) {
        console.error('Error adding copy code buttons:', error);
      }
    }

    /**
     * Sanitize input to prevent XSS attacks
     * @param {string} input - Raw input string
     * @returns {string} - Sanitized string
     */
    sanitizeInput(input) {
      if (typeof input !== 'string') return '';

      // Remove potentially dangerous HTML tags and attributes
      const dangerousTags = /<(script|iframe|object|embed|form|input|meta|link|style)[^>]*>.*?<\/\1>|<(script|iframe|object|embed|form|input|meta|link|style)[^>]*\/?>|javascript:|data:|vbscript:|on\w+\s*=/gi;

      return input.replace(dangerousTags, '');
    }

    /**
     * Escape HTML characters to prevent XSS
     * @param {string} text - Text to escape
     * @returns {string} - Escaped text
     */
    escapeHtml(text) {
      if (typeof text !== 'string') return '';

      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    /**
     * Sanitize and validate URLs
     * @param {string} url - URL to sanitize
     * @returns {string|null} - Sanitized URL or null if invalid
     */
    sanitizeUrl(url) {
      if (typeof url !== 'string') return null;

      try {
        // Remove dangerous protocols
        if (/^(javascript|data|vbscript):/i.test(url)) {
          return null;
        }

        // Ensure URL starts with http or https
        if (!/^https?:\/\//i.test(url)) {
          url = 'https://' + url;
        }

        // Validate URL format
        const urlObj = new URL(url);
        return urlObj.href;
      } catch (e) {
        return null;
      }
    }

    /**
     * Rate limiting for user actions
     * @param {string} action - Action identifier
     * @param {number} limit - Maximum actions per minute
     * @returns {boolean} - Whether action is allowed
     */
    checkRateLimit(action, limit = 10) {
      const now = Date.now();
      const minute = 60 * 1000;

      if (!this.rateLimits) {
        this.rateLimits = new Map();
      }

      if (!this.rateLimits.has(action)) {
        this.rateLimits.set(action, []);
      }

      const timestamps = this.rateLimits.get(action);

      // Remove old timestamps
      while (timestamps.length > 0 && now - timestamps[0] > minute) {
        timestamps.shift();
      }

      // Check if limit exceeded
      if (timestamps.length >= limit) {
        return false;
      }

      // Add current timestamp
      timestamps.push(now);
      return true;
    }

    showTypingIndicator() {
      const $typingIndicator = $(`
                <div class="qi-message qi-assistant-message qi-typing-indicator"
                    role="status"
                    aria-label="Assistant is thinking">
                    <div class="qi-typing-content">
                        <div class="qi-typing-text">Thinking</div>
                        <div class="qi-typing-dots">
                            <div class="qi-typing-dot"></div>
                            <div class="qi-typing-dot"></div>
                            <div class="qi-typing-dot"></div>
                        </div>

                    </div>
                    <div class="skeleton-container">
                        <div class="skeleton-line small"></div>
                        <div class="skeleton-line medium"></div>
                        <div class="skeleton-line large"></div>
                        <div class="skeleton-line short"></div>

                        <div class="skeleton-line large"></div>
                        <div class="skeleton-line small"></div>
                        <div class="skeleton-line medium"></div>
                        <div class="skeleton-line short"></div>

                        <div class="skeleton-line medium"></div>
                        <div class="skeleton-line large"></div>
                        <div class="skeleton-line small"></div>

                        <div class="skeleton-line short"></div>
                        <div class="skeleton-line medium"></div>
                        <div class="skeleton-line small"></div>
                    </div>
                </div>
            `);
      this.$messages.append($typingIndicator);
      this.scrollToBottom();
    }

    hideTypingIndicator() {
      this.$messages.find(".qi-typing-indicator").remove();
    }

    showError(message) {
      // Create and show error notification
      const $notification = $("<div>", {
        class: "qi-notification qi-notification-error",
        html: `
                    <i class="fas fa-exclamation-circle"></i>
                    <span>${message}</span>
                `,
      }).appendTo(this.$container);

      // Auto-hide after 3 seconds
      setTimeout(() => {
        $notification.fadeOut(200, function () {
          $(this).remove();
        });
      }, 3000);
    }

    scrollToBottom() {
      try {
        if (this.$messages && this.$messages.length) {
          const messagesContainer = this.$messages[0];

          // Use throttled scrolling for better performance
          if (!this.scrollThrottled) {
            this.scrollThrottled = this.throttle(() => {
              const isAtBottom = messagesContainer.scrollTop + messagesContainer.clientHeight >= messagesContainer.scrollHeight - 10;

              // Only scroll if user is already at or near the bottom or if typing
              if (isAtBottom || this.isTyping) {
                // Use requestAnimationFrame for smooth scrolling
                requestAnimationFrame(() => {
                  messagesContainer.scrollTop = messagesContainer.scrollHeight;
                });
              }
            }, 16); // ~60fps
          }

          this.scrollThrottled();
        }
      } catch (error) {
        console.error("Error scrolling to bottom:", error);
      }
    }

    adjustMessageContainerHeight() {
      // Only proceed if we have messages
      if (!this.$messages.length) return;

      // Get the container height
      const containerHeight = this.$container.height();

      // Get the header height
      const headerHeight =
        this.$container.find(".qi-chat-header").outerHeight() || 0;

      // Get the input container height
      const inputHeight =
        this.$container.find(".qi-input-container").outerHeight() || 0;

      // Calculate available height for messages
      let messagesHeight = containerHeight - headerHeight - inputHeight - 80; // 40px for margins

      // Adjust for mobile layout where sidebar is below
      if (window.innerWidth <= 768 && !this.isSidebarHidden) {
        const sidebarHeight =
          this.$container.find(".qi-sidebar").outerHeight() || 0;
        messagesHeight -= sidebarHeight;
      }

      // Set the max-height of the messages container
      // This ensures the input stays in view even with long messages
      this.$messages.css("max-height", messagesHeight + "px");

      // Ensure the chat wrapper uses flex layout
      const $wrapper = this.$container.find(".qi-chat-wrapper");
      if (!$wrapper.hasClass("qi-flex-layout")) {
        $wrapper.addClass("qi-flex-layout");
      }

      // Make sure the input container is visible
      const $inputContainer = this.$container.find(".qi-input-container");
      $inputContainer.css("position", "sticky");
      $inputContainer.css("bottom", "0");
      $inputContainer.css("z-index", "1000");

      // Scroll to bottom to ensure latest messages are visible
      this.scrollToBottom();
    }

    setCurrentAssistant(assistantId) {
      // Save current chat before switching if there are messages
      if (this.$messages.find(".qi-message").length > 0) {
        this.saveCurrentChatToHistory()
          .then(() => {
            this.switchAssistant(assistantId);
          })
          .catch((error) => {
            console.error("Failed to save chat history:", error);
            // Still switch assistant even if save fails
            this.switchAssistant(assistantId);
          });
      } else {
        this.switchAssistant(assistantId);
      }
    }

    // Add new method to handle the actual assistant switch
    switchAssistant(assistantId) {
      console.log("Switching to assistant:", assistantId);
      this.currentAssistant = assistantId;

      // Update the select value
      this.$container.find(".qi-assistant-select").val(assistantId);

      // Update active state in assistant list
      this.$container.find(".qi-assistant-item").removeClass("active");
      this.$container
        .find(`.qi-assistant-item[data-assistant-id="${assistantId}"]`)
        .addClass("active");

      // Get assistant info and update placeholder
      const selectedOption = this.$container.find(
        ".qi-assistant-select option:selected"
      );
      const assistantName = selectedOption.text().trim();
      this.updateInputPlaceholder(assistantName);

      // Save current assistant ID to user preferences
      if (assistantId) {
        $.ajax({
          url: qkbChatbot.ajax_url,
          type: "POST",
          data: {
            action: "qkb_save_current_assistant",
            nonce: qkbChatbot.nonce,
            assistant_id: assistantId,
          },
        });
      }

      // Only proceed with assistant-specific actions if we have a valid assistant ID
      if (assistantId) {
        // Reset the chat wrapper class to show welcome message
        const $wrapper = this.$container.find(".qi-chat-wrapper");
        $wrapper.removeClass("has-messages");

        // Force a reflow to ensure the CSS changes take effect
        void $wrapper[0].offsetWidth;

        // Clear the current chat
        this.$messages.empty();
        this.currentChatId = null;
        this.$container.find(".qi-history-item").removeClass("active");

        if (this.currentAssistant) {
          this.chatHistory[this.currentAssistant] = [];
          this.saveChatHistory();
        }

        // Show welcome message with a slight delay to ensure CSS changes take effect
        setTimeout(() => {
          this.showAssistantWelcome(assistantId);
        }, 50);

        // Update the chat history
        this.refreshChatHistory();
      } else {
        // Show a message when no assistants are available
        this.$messages.html(`
                    <div class="qi-welcome-message">
                        <div class="qi-welcome-header">
                            <div class="qi-welcome-title">
                                <h2>No Assistants</h2>
                                <span>Please contact your administrator to get access to assistants.</span>
                            </div>
                        </div>
                    </div>
                `);

        // Clear history display
        this.$container.find(".qi-history-wrapper").empty();
      }
    }

    updateInputPlaceholder(assistantName) {
      const template = this.$input.data("placeholder-template");
      const newPlaceholder = template.replace(
        "%s",
        assistantName || "Assistant"
      );
      this.$input.attr("placeholder", newPlaceholder);
    }

    handleAssistantSelect(e) {
      const $assistant = $(e.currentTarget);
      const assistantId = $assistant.data("assistant-id");
      if (assistantId) {
        // Only switch if we have a valid assistant ID
        this.setCurrentAssistant(assistantId);
      }
    }

    startNewChat() {
      console.log("Starting new chat...");
      this.showModal(
        "Start New Chat",
        "Are you sure you want to start a new chat? This will clear your current conversation.",
        () => {
          // Save current chat before clearing if not continuing an existing chat
          if (!this.currentChatId) {
            this.saveCurrentChatToHistory()
              .then(() => {
                // Reset the chat wrapper class to show welcome message
                const $wrapper = this.$container.find(".qi-chat-wrapper");
                $wrapper.removeClass("has-messages");

                // Force a reflow to ensure the CSS changes take effect
                void $wrapper[0].offsetWidth;

                // Clear the chat
                this.clearChat();
              })
              .catch((error) => {
                console.error("Failed to save chat history:", error);

                // Reset the chat wrapper class to show welcome message
                const $wrapper = this.$container.find(".qi-chat-wrapper");
                $wrapper.removeClass("has-messages");

                // Force a reflow to ensure the CSS changes take effect
                void $wrapper[0].offsetWidth;

                // Clear the chat even if save fails
                this.clearChat();
              });
          } else {
            // Reset the chat wrapper class to show welcome message
            const $wrapper = this.$container.find(".qi-chat-wrapper");
            $wrapper.removeClass("has-messages");

            // Force a reflow to ensure the CSS changes take effect
            void $wrapper[0].offsetWidth;

            // Clear the chat
            this.clearChat();
          }
        }
      );
    }

    saveCurrentChatToHistory() {
      return new Promise((resolve, reject) => {
        const messages = this.$messages
          .find(".qi-message")
          .map((_, el) => {
            const $msg = $(el);
            return {
              content: $msg.find(".qi-message-content").html(),
              type: $msg.hasClass("qi-user-message") ? "user" : "assistant",
              timestamp: $msg.data("timestamp") || Date.now(),
            };
          })
          .get();

        // Don't save if there are no messages
        if (messages.length === 0) {
          resolve();
          return;
        }

        // Get the first user message as the chat title
        const firstUserMessage = messages.find((msg) => msg.type === "user");
        const title = firstUserMessage
          ? firstUserMessage.content.substring(0, 50) +
            (firstUserMessage.content.length > 50 ? "..." : "")
          : "Chat " + new Date().toLocaleString();

        $.ajax({
          url: qkbChatbot.ajax_url,
          type: "POST",
          data: {
            action: "qkb_save_chat_history",
            nonce: qkbChatbot.nonce,
            assistant_id: this.currentAssistant,
            title: title,
            messages: messages,
          },
          success: (response) => {
            if (response.success) {
              // Refresh the history display
              this.refreshChatHistory();
              resolve();
            } else {
              reject(response.data);
            }
          },
          error: (_, __, error) => {
            reject(error);
          },
        });
      });
    }

    refreshChatHistory() {
      $.ajax({
        url: qkbChatbot.ajax_url,
        type: "POST",
        data: {
          action: "qkb_get_chat_history",
          nonce: qkbChatbot.nonce,
          assistant_id: this.currentAssistant,
        },
        success: (response) => {
          if (response.success) {
            const $historyWrapper = this.$container.find(".qi-history-wrapper");
            $historyWrapper.html(response.data);
          }
        },
        error: (_, __, error) => {
          console.error("Failed to refresh chat history:", error);
        },
      });
    }

    clearAllHistory() {
      this.showModal(
        "Clear Chat History",
        "Are you sure you want to clear all chat history? This action cannot be undone.",
        () => {
          $.ajax({
            url: qkbChatbot.ajax_url,
            type: "POST",
            data: {
              action: "qkb_clear_chat_history",
              nonce: qkbChatbot.nonce,
              assistant_id: this.currentAssistant,
            },
            success: (response) => {
              if (response.success) {
                // Clear only current assistant history
                if (this.currentAssistant) {
                  this.chatHistory[this.currentAssistant] = [];
                }
                this.$messages.empty();
                this.currentChatId = null;
                this.refreshChatHistory();
                // Show welcome message after clearing history
                this.showAssistantWelcome(this.currentAssistant);
              }
            },
            error: (_, __, error) => {
              console.error("Failed to clear chat history:", error);
            },
          });
        }
      );
    }

    toggleFullscreen() {
      const isFullscreen = this.$container.attr("data-fullscreen") === "true";
      this.$container.attr("data-fullscreen", !isFullscreen);

      // Only update the button if it exists
      if (this.$toggleFullscreenButton && this.$toggleFullscreenButton.length) {
        this.$toggleFullscreenButton
          .find("i")
          .toggleClass("fa-expand", isFullscreen)
          .toggleClass("fa-compress", !isFullscreen);
      }

      if (!isFullscreen) {
        $("body").addClass("qi-fullscreen-active");
      } else {
        $("body").removeClass("qi-fullscreen-active");
      }

      // Adjust container size after toggling fullscreen
      setTimeout(() => {
        this.adjustMessageContainerHeight();
        this.scrollToBottom();
      }, 100);
    }

    loadChatHistory() {
      try {
        const saved = localStorage.getItem("qi_chat_history");
        if (saved) {
          const parsed = JSON.parse(saved);
          if (Array.isArray(parsed)) {
            // Convert old format to new format
            this.chatHistory = {};
            parsed.forEach((msg) => {
              if (msg.assistantId) {
                if (!this.chatHistory[msg.assistantId]) {
                  this.chatHistory[msg.assistantId] = [];
                }
                this.chatHistory[msg.assistantId].push(msg);
              }
            });
          } else {
            this.chatHistory = parsed;
          }
        }
      } catch (error) {
        console.error("Error loading chat history:", error);
        this.chatHistory = {};
      }
    }

    loadAssistantHistory(assistantId) {
      this.$messages.empty();
      if (this.chatHistory[assistantId]) {
        this.chatHistory[assistantId].forEach((msg) => {
          this.addMessage(msg.content, msg.type);
        });
      }
    }

    saveChatHistory() {
      try {
        localStorage.setItem(
          "qi_chat_history",
          JSON.stringify(this.chatHistory)
        );
      } catch (error) {
        console.error("Error saving chat history:", error);
      }
    }

    initializeAssistantSelector() {
      // Get stored assistant ID from localStorage, fallback to first available assistant
      const storedAssistantId = localStorage.getItem("qi_current_assistant");
      const $firstAssistant = this.$assistantItems.first();

      if ($firstAssistant.length) {
        // If we have assistants available
        if (storedAssistantId) {
          this.setCurrentAssistant(storedAssistantId);
        } else {
          this.setCurrentAssistant($firstAssistant.data("assistant-id"));
        }
      } else {
        // If no assistants are available, set to empty value
        this.setCurrentAssistant("");
      }

      // Update localStorage when assistant changes
      this.$assistantItems.on("click", (e) => {
        const assistantId = $(e.currentTarget).data("assistant-id");
        if (assistantId) {
          // Only store if we have a valid assistant ID
          localStorage.setItem("qi_current_assistant", assistantId);
        }
        this.setCurrentAssistant(assistantId);
      });
    }

    // Modify loadHistoryItem method
    loadHistoryItem(e) {
      const $item = $(e.currentTarget);
      const chatId = $item.data("chat-id");
      const assistantId = $item.data("assistant-id");

      // Store the current chat ID
      this.currentChatId = chatId;

      $.ajax({
        url: qkbChatbot.ajax_url,
        type: "POST",
        data: {
          action: "qkb_load_chat_history_item",
          nonce: qkbChatbot.nonce,
          timestamp: chatId,
          assistant_id: assistantId,
        },
        success: (response) => {
          if (response.success && response.data.messages) {
            // Clear current chat
            this.$messages.empty();

            // Load the messages directly without typing effect
            response.data.messages.forEach((message) => {
              const messageId = Date.now();
              const $message = $("<div>", {
                class: `qi-message qi-${message.type}-message`,
                "data-message-id": messageId,
              });

              const $content = $("<div>", {
                class: "qi-message-content",
                html: this.formatMessage(message.content),
              });

              $message.append($content);

              // Create message actions container
              const $messageActions = $("<div>", {
                class: "qi-message-actions visible",
              });

              if (message.type === "assistant") {
                // Copy button for assistant messages
                const $copyButton = $("<button>", {
                  class: "qi-action-button qi-copy-button",
                  html: '<i class="fas fa-copy"></i>',
                  title: "Copy to clipboard",
                }).on("click", (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  this.copyMessageToClipboard($message);
                });

                // Feedback buttons
                const $feedbackContainer = $("<div>", {
                  class: "qi-feedback-container",
                });

                const $thumbsUpButton = $("<button>", {
                  class: "qi-feedback-button qi-thumbs-up",
                  html: '<i class="fas fa-thumbs-up"></i>',
                  title: "This was helpful",
                  "data-feedback": "positive",
                }).on("click", (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  this.submitFeedback(messageId, "positive");
                });

                const $thumbsDownButton = $("<button>", {
                  class: "qi-feedback-button qi-thumbs-down",
                  html: '<i class="fas fa-thumbs-down"></i>',
                  title: "This was not helpful",
                  "data-feedback": "negative",
                }).on("click", (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  this.submitFeedback(messageId, "negative");
                });

                $feedbackContainer.append($thumbsUpButton, $thumbsDownButton);
                $messageActions.append($copyButton, $feedbackContainer);
                $message.append($messageActions);
              } else if (message.type === "user") {
                // User message actions have been removed
                $message.append($messageActions);
              }

              this.$messages.append($message);

              // Apply syntax highlighting to assistant messages
              if (message.type === "assistant") {
                this.applySyntaxHighlighting($message);
              }

              this.scrollToBottom();
            });

            // Highlight the active history item
            this.$container.find(".qi-history-item").removeClass("active");
            $item.addClass("active");
          }
        },
        error: (_, __, error) => {
          console.error("Failed to load chat history:", error);
          this.showError("Failed to load chat history");
        },
      });
    }

    // Add new method to update chat history
    updateChatHistory() {
      const messages = this.$messages
        .find(".qi-message")
        .map((_, el) => {
          const $msg = $(el);
          return {
            content: $msg.find(".qi-message-content").html(),
            type: $msg.hasClass("qi-user-message") ? "user" : "assistant",
            timestamp: $msg.data("timestamp") || Date.now(),
          };
        })
        .get();

      $.ajax({
        url: qkbChatbot.ajax_url,
        type: "POST",
        data: {
          action: "qkb_update_chat_history",
          nonce: qkbChatbot.nonce,
          chat_id: this.currentChatId,
          assistant_id: this.currentAssistant,
          messages: messages,
        },
        success: (response) => {
          if (response.success) {
            this.refreshChatHistory();
          }
        },
        error: (xhr, status, error) => {
          console.error("Failed to update chat history:", error);
        },
      });
    }

    // Add helper method to clear chat
    clearChat() {
      console.log("Clearing chat...");

      // Remove all messages
      this.$messages.empty();

      // Reset chat state
      this.currentChatId = null;
      this.$container.find(".qi-history-item").removeClass("active");

      // Reset the chat wrapper class
      const $wrapper = this.$container.find(".qi-chat-wrapper");
      $wrapper.removeClass("has-messages");

      // Force a reflow to ensure the CSS changes take effect
      void $wrapper[0].offsetWidth;

      if (this.currentAssistant) {
        this.chatHistory[this.currentAssistant] = [];
        this.saveChatHistory();

        // Show welcome message after clearing chat
        this.showAssistantWelcome(this.currentAssistant);
      } else {
        console.warn("No current assistant set when clearing chat");

        // Fallback welcome message if no assistant is set
        const fallbackWelcome = `
                    <div class="qi-welcome-message">
                        <div class="qi-welcome-header">
                            <div class="qi-welcome-title">
                                <h2>Hello, ${this.username}</h2>
                                <span>How can I assist you today?</span>
                            </div>
                        </div>
                    </div>
                `;
        this.$messages.html(fallbackWelcome);
        this.$container.find(".qi-welcome-message").show();
      }
    }

    // Add new method to handle history item deletion
    handleHistoryItemDelete(e) {
      const $item = $(e.currentTarget).closest(".qi-history-item");
      const chatId = $item.data("chat-id");
      const chatTitle = $item.find(".qi-history-title").text().trim();

      this.showModal(
        "Delete Chat",
        `Are you sure you want to delete "${chatTitle}"? This action cannot be undone.`,
        () => {
          $.ajax({
            url: qkbChatbot.ajax_url,
            type: "POST",
            data: {
              action: "qkb_delete_chat_history",
              nonce: qkbChatbot.nonce,
              timestamp: chatId,
            },
            success: (response) => {
              if (response.success) {
                // If this was the active chat, clear it
                if (this.currentChatId === chatId) {
                  this.clearChat();
                }

                // Remove the item with animation
                $item.fadeOut(200, () => {
                  $item.remove();
                  // If no items left, refresh to show empty state
                  if (this.$container.find(".qi-history-item").length === 0) {
                    this.refreshChatHistory();
                  }
                });
              }
            },
            error: (xhr, status, error) => {
              console.error("Failed to delete chat history item:", error);
              this.showError("Failed to delete chat history item");
            },
          });
        }
      );
    }

    // Add new method to get assistant info and show welcome message
    showAssistantWelcome(assistantId) {
      console.log("Showing welcome message for assistant:", assistantId);

      // Clear existing messages
      this.$messages.empty();

      // Reset the chat wrapper class to show welcome message
      const $wrapper = this.$container.find(".qi-chat-wrapper");
      $wrapper.removeClass("has-messages");

      // Force a reflow to ensure the CSS changes take effect
      void $wrapper[0].offsetWidth;

      // Create a loading placeholder for the welcome message
      const loadingWelcome = `
                <div class="qi-welcome-message" style="opacity: 0.7;">
                    <div id="qi-particles-js" class="qi-particles-container"></div>
                    <div class="qi-welcome-content">
                        <div class="qi-welcome-header">
                            <div class="qi-welcome-title">
                                <h2>Hello, ${this.username}</h2>
                                <span>Loading Assistant Information...</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

      // Show loading state
      this.$messages.html(loadingWelcome);

      // Initialize particles for loading state
      this.initializeParticlesForWelcome();

      // Get assistant info
      $.ajax({
        url: qkbChatbot.ajax_url,
        type: "POST",
        data: {
          action: "qkb_get_assistant_info",
          nonce: qkbChatbot.nonce,
          assistant_id: assistantId,
        },
        success: (response) => {
          if (response.success) {
            // Use the data from the response
            let suggestedPromptsHtml = "";

            // Check if we have suggested prompts
            if (
              response.data.suggested_prompts &&
              response.data.suggested_prompts.length > 0
            ) {
              const promptButtons = response.data.suggested_prompts
                .map(
                  (prompt) =>
                    `<button class="qi-prompt-button">${prompt}</button>`
                )
                .join("");

              suggestedPromptsHtml = `
                                <div class="qi-suggested-prompts">
                                    <h3>Try asking:</h3>
                                    <div class="qi-prompt-buttons">
                                        ${promptButtons}
                                    </div>
                                </div>
                            `;
            } else {
              // Default prompts if none are provided
              suggestedPromptsHtml = `
                                <div class="qi-suggested-prompts">
                                    <h3>Try asking:</h3>
                                    <div class="qi-prompt-buttons">
                                        <button class="qi-prompt-button">What can you help me with?</button>
                                        <button class="qi-prompt-button">How do I get started?</button>
                                        <button class="qi-prompt-button">What can you assist me with?</button>
                                    </div>
                                </div>
                            `;
            }

            const welcomeMessage = `
                            <div class="qi-welcome-message">
                                <div id="qi-particles-js" class="qi-particles-container"></div>
                                <div class="qi-welcome-content">
                                    <div class="qi-welcome-header">
                                        <div class="qi-welcome-title">
                                            <h2>Hello, ${this.username}</h2>
                                            <span>How can I assist you today?</span>
                                        </div>
                                    </div>
                                    ${suggestedPromptsHtml}
                                </div>
                            </div>
                        `;

            // Add the welcome message with animation
            this.$messages.fadeOut(200, () => {
              this.$messages.html(welcomeMessage).fadeIn(200, () => {
                // Make sure the welcome message is visible
                this.$container.find(".qi-welcome-message").show();

                // Initialize the prompt buttons
                this.initSuggestedPrompts();

                // Reinitialize particles.js
                this.initializeParticlesForWelcome();

                // Adjust message container height for responsive layout
                this.adjustMessageContainerHeight();

                // Log success
                console.log("Welcome message displayed successfully");
              });
            });
          } else {
            console.error("Failed to get assistant info:", response);
            this.showFallbackWelcome();
          }
        },
        error: (error) => {
          // Log the error
          console.error("AJAX error when getting assistant info:", error);

          // Show fallback welcome message
          this.showFallbackWelcome();
        },
      });
    }

    // Helper method to show fallback welcome message
    showFallbackWelcome() {
      // Fallback welcome message if AJAX fails
      const fallbackWelcome = `
                <div class="qi-welcome-message">
                    <div id="qi-particles-js" class="qi-particles-container"></div>
                    <div class="qi-welcome-content">
                        <div class="qi-welcome-header">
                            <div class="qi-welcome-title">
                                <h2>Hello, ${this.username}</h2>
                                <span>How can I assist you today?</span>
                            </div>
                        </div>
                        <div class="qi-suggested-prompts">
                            <h3>Try asking:</h3>
                            <div class="qi-prompt-buttons">
                                <button class="qi-prompt-button"> What can you help me with?</button>
                                <button class="qi-prompt-button"> How do I get started?</button>
                                <button class="qi-prompt-button"> What can you assist me with?</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

      this.$messages.fadeOut(200, () => {
        this.$messages.html(fallbackWelcome).fadeIn(200, () => {
          // Initialize the prompt buttons
          this.initSuggestedPrompts();

          // Reinitialize particles.js
          this.initializeParticlesForWelcome();

          // Adjust message container height for responsive layout
          this.adjustMessageContainerHeight();

          console.log("Fallback welcome message displayed");
        });
      });
    }

    // Initialize suggested prompts functionality
    initSuggestedPrompts() {
      // Add animation delay to each button
      this.$container.find(".qi-prompt-button").each(function (index) {
        $(this).css("--button-index", index);
      });

      // Add click handler
      this.$container.find(".qi-prompt-button").on("click", (e) => {
        const promptText = $(e.currentTarget).text();
        this.$input.val(promptText);
        this.$input.trigger("input"); // Trigger input event for character counter

        // Enable send button
        this.$sendButton.prop("disabled", false);

        // Focus the textarea
        this.$input.focus();

        // Add a subtle animation to the clicked button
        $(e.currentTarget).addClass("clicked");
        setTimeout(() => {
          $(e.currentTarget).removeClass("clicked");
        }, 500);
      });
    }

    // Initialize particles.js for welcome message
    initializeParticlesForWelcome() {
      // Check if particles.js is available
      if (typeof window.initializeParticlesBackground === "function") {
        // Trigger custom event to reinitialize particles
        $(document).trigger("reinitialize-particles");

        // As a fallback, also try to call the function directly
        setTimeout(() => {
          if ($("#qi-particles-js").length) {
            console.log("Direct call to initialize particles");
            window.initializeParticlesBackground();
          }
        }, 100);
      } else {
        console.warn("Particles.js initialization function not found");
      }
    }

    /**
     * Add a system message to the chat
     *
     * @param {string} message Message text
     * @returns {jQuery} Message element
     */
    addSystemMessage(message) {
      if (!message.trim()) return;

      const $message = $("<div>", {
        class: "qi-message qi-system-message",
        "data-message-id": "system_" + Date.now(),
      });

      const $content = $("<div>", {
        class: "qi-message-content",
        text: message,
      });

      $message.append($content);
      this.$messagesContainer.append($message);
      this.scrollToBottom();

      return $message;
    }

    toggleSidebar() {
      this.isSidebarHidden = !this.isSidebarHidden;
      this.$container.attr("data-sidebar-hidden", this.isSidebarHidden);

      // Update button icon
      this.$toggleSidebarButton
        .find("i")
        .toggleClass("fa-bars", !this.isSidebarHidden)
        .toggleClass("fa-bars-staggered", this.isSidebarHidden);
    }

    handleResize() {
      // Auto-hide sidebar on mobile if window width is less than 768px
      if (window.innerWidth <= 768 && !this.isSidebarHidden) {
        this.isSidebarHidden = true;
        this.$container.attr("data-sidebar-hidden", true);
        this.$toggleSidebarButton
          .find("i")
          .removeClass("fa-bars")
          .addClass("fa-bars-staggered");
      }

      // Adjust message container height for better responsiveness
      this.adjustMessageContainerHeight();

      // Apply grid layout classes based on screen size
      this.updateLayoutClasses();

      // Scroll to bottom to ensure latest messages are visible
      this.scrollToBottom();
    }

    // Helper method to update layout classes based on screen size
    updateLayoutClasses() {
      const width = window.innerWidth;

      if (width <= 768) {
        // Mobile layout
        this.$container.addClass("qi-mobile-layout");
      } else {
        // Desktop layout
        this.$container.removeClass("qi-mobile-layout");
      }
    }

    initializeKnowledgeBase() {
      // Ensure the Knowledge Base Modal and Form are selected within the container
      this.$kbModal = this.$container.find(".qi-kb-modal");
      this.$kbForm = this.$kbModal.find(".qi-kb-form");

      // Toggle content type fields
      this.$container.on("change", "#kb-type", (e) => {
        const type = $(e.target).val();
        this.$container.find(".kb-content").fadeOut(200, () => {
          this.$container.find(`.kb-content-${type}`).fadeIn(200);
        });
      });

      // Open modal - add a "show" class for CSS transitions
      this.$addKnowledgeButton.on("click", () => {
        this.$kbModal.fadeIn(200, () => {
          this.$kbModal.addClass("show");
        });
      });

      // Close modal (including the global close button)
      this.$container.on(
        "click",
        ".qi-kb-modal-close, .qi-kb-cancel, .qi-kb-modal-close-global",
        () => {
          this.$kbModal.fadeOut(200, () => {
            this.$kbModal.removeClass("show");
          });
          if (this.$kbForm.length) {
            this.$kbForm[0].reset();
          }
          this.$container.find(".kb-content").hide();
        }
      );

      // Handle form submission
      this.$kbForm.on("submit", (e) => {
        e.preventDefault();
        this.submitKnowledgeBase();
      });

      // File upload enhancements - scope all selectors to the container
      const $fileGroup = this.$container.find(".kb-content-file");
      const $fileInput = this.$container.find("#kb-file");

      // Use event delegation for drag and drop events
      this.$container.on("dragover", ".kb-content-file", (e) => {
        e.preventDefault();
        $(e.currentTarget).addClass("dragover");
      });

      this.$container.on("dragleave", ".kb-content-file", (e) => {
        e.preventDefault();
        $(e.currentTarget).removeClass("dragover");
      });

      this.$container.on("drop", ".kb-content-file", (e) => {
        e.preventDefault();
        $(e.currentTarget).removeClass("dragover");
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
          this.handleFileSelect(files[0]);
        }
      });

      this.$container.on("change", "#kb-file", (e) => {
        if (e.target.files.length > 0) {
          console.log("File selected from input:", e.target.files[0].name);
          this.handleFileSelect(e.target.files[0]);
        } else {
          console.log("No file selected from input");
        }
      });

      // --- Enhanced Tab switching for Knowledge Base Modal with animations ---
      this.$kbModal.find(".qi-kb-tab").on("click", (e) => {
        const $tab = $(e.currentTarget);
        const tab = $tab.data("tab"); // "add" or "manage"

        // Skip if this tab is already active
        if ($tab.hasClass("active")) return;

        // Get current active tab content and new tab content
        const $currentContent = this.$kbModal.find(
          ".qi-kb-tab-content:visible"
        );
        const $newContent = this.$kbModal.find(
          ".qi-" + tab + "-knowledge-content"
        );

        // Remove active class from all tabs
        this.$kbModal.find(".qi-kb-tab").removeClass("active");

        // Add active class to clicked tab
        $tab.addClass("active");

        // Animate tab transition
        if ($currentContent.length) {
          // Add leaving class to current content
          $currentContent.addClass("leaving");

          // After animation completes, hide the old content and show the new one
          setTimeout(() => {
            $currentContent.hide().removeClass("leaving");
            $newContent.addClass("entering").show();

            // Remove the entering class after animation completes
            setTimeout(() => {
              $newContent.removeClass("entering");
            }, 400);

            // If "Manage Knowledge" is selected, load knowledge entries via AJAX
            if (tab === "manage") {
              this.loadManageKnowledge();
            }
          }, 300);
        } else {
          // No current content, just show the new one with animation
          $newContent.addClass("entering").show();

          // Remove the entering class after animation completes
          setTimeout(() => {
            $newContent.removeClass("entering");
          }, 400);

          // If "Manage Knowledge" is selected, load knowledge entries via AJAX
          if (tab === "manage") {
            this.loadManageKnowledge();
          }
        }
      });

      // --- New Code: Handle knowledge deletion from Manage tab ---
      this.$kbModal.on("click", ".qi-knowledge-delete", (e) => {
        const $btn = $(e.currentTarget);
        const $item = $btn.closest(".qi-knowledge-item");
        const knowledgeId = $item.data("id");
        if (confirm("Are you sure you want to delete this knowledge entry?")) {
          $.ajax({
            url: qkbChatbot.ajax_url,
            type: "POST",
            data: {
              action: "qkb_delete_knowledge",
              nonce: qkbChatbot.nonce,
              knowledge_id: knowledgeId,
            },
            success: (response) => {
              if (response.success) {
                $item.fadeOut(200, function () {
                  $(this).remove();
                });
              } else {
                this.showError(response.data || "Deletion failed");
              }
            },
            error: (xhr, status, error) => {
              console.error("Failed to delete knowledge:", error);
              this.showError("Failed to delete knowledge.");
            },
          });
        }
      });
    }

    async submitKnowledgeBase() {
      const $form = this.$kbForm;
      // Create and show a loading overlay on the KB modal
      const $loadingIndicator = $(
        '<div class="qi-loading-overlay"><div class="qi-loading-spinner"></div></div>'
      );
      this.$kbModal.append($loadingIndicator);

      const formData = new FormData($form[0]);
      formData.append("action", "qkb_add_knowledge");
      formData.append("nonce", qkbChatbot.nonce);

      // Check if we're uploading a file
      const contentType = $form.find("#kb-type").val();
      if (contentType === "file") {
        console.log("File upload selected, checking for file...");

        // If we have a stored file reference, use it instead of the form's file input
        if (this.selectedFile) {
          console.log("Using stored file reference:", this.selectedFile.name);
          // Remove any existing file entry that might be in the FormData
          formData.delete("file");
          // Add our stored file reference
          formData.append("file", this.selectedFile);
        } else {
          // Check if there's a file in the input
          const fileInput = $form.find("#kb-file")[0];
          if (fileInput && fileInput.files && fileInput.files.length > 0) {
            console.log("Using file from input:", fileInput.files[0].name);
            // We have a file in the input, make sure it's in the FormData
            formData.delete("file");
            formData.append("file", fileInput.files[0]);
          } else {
            // No file selected
            console.error("No file selected for upload");
            this.showError("Please select a file to upload");
            $form.find(".qi-kb-submit").prop("disabled", false).text("Submit");
            $loadingIndicator.remove();
            return;
          }
        }

        // Verify the file is in the FormData
        console.log("FormData contains file:", formData.has("file"));
      }

      try {
        const $submit = $form.find(".qi-kb-submit");
        $submit.prop("disabled", true).text("Submitting...");

        const response = await $.ajax({
          url: qkbChatbot.ajax_url,
          type: "POST",
          data: formData,
          processData: false,
          contentType: false,
        });

        if (response.success) {
          this.showSuccess("Content submitted successfully!");
          this.$kbModal.fadeOut(200);
          $form[0].reset();
          $(".kb-content").hide();

          // Clear the stored file reference
          this.selectedFile = null;

          // Reset the file input UI
          const $fileName = this.$container.find(".file-name");
          $fileName.hide().empty();
          this.$container
            .find(".file-input-label")
            .html('<i class="fas fa-cloud-upload-alt"></i> Choose a file');
          this.$container.find(".upload-progress").hide();
          this.$container.find(".upload-status").hide();
        } else {
          this.showError(response.data || "Failed to submit content");
        }
      } catch (error) {
        console.error("Error submitting content:", error);
        this.showError("Failed to submit content. Please try again.");
      } finally {
        $form.find(".qi-kb-submit").prop("disabled", false).text("Submit");
        $loadingIndicator.remove();
      }
    }

    showSuccess(message) {
      // Create and show success notification
      const $notification = $("<div>", {
        class: "qi-notification qi-notification-success",
        html: `
                    <i class="fas fa-check-circle"></i>
                    <span>${message}</span>
                `,
      }).appendTo(this.$container);

      // Auto-hide after 3 seconds
      setTimeout(() => {
        $notification.fadeOut(200, function () {
          $(this).remove();
        });
      }, 3000);
    }

    showExportModal() {
      const modalHtml = `
                <div class="qi-export-modal">
                    <div class="qi-export-modal-content">
                        <div class="qi-export-modal-header">
                            <h3>Export Chat Transcript</h3>
                            <button class="qi-export-modal-close">&times;</button>
                        </div>
                        <div class="qi-export-modal-body">
                            <div class="qi-export-type-options">
                                <label>
                                    <input type="radio" name="qi-export-type" value="all" checked>
                                    <strong>Full Transcript:</strong>
                                    <span class="qi-export-type-desc">Includes both user and assistant messages</span>
                                </label>
                                <label>
                                    <input type="radio" name="qi-export-type" value="assistant">
                                    <strong>Assistant Only:</strong>
                                    <span class="qi-export-type-desc">Only includes the assistant's responses</span>
                                </label>
                            </div>
                            <div class="qi-export-format-options">
                                <p>Select format:</p>
                                <div class="qi-export-options">
                                    <button class="qi-export-option" data-format="pdf">
                                        <i class="fas fa-file-pdf"></i>
                                        <div class="qi-export-format-info">
                                            <span class="qi-export-option-label">PDF Document</span>
                                       </div>
                                    </button>
                                    <button class="qi-export-option" data-format="text">
                                        <i class="fas fa-file-alt"></i>
                                        <div class="qi-export-format-info">
                                            <span class="qi-export-option-label">Text File</span>
                                      </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

      // Remove existing modal if present
      this.$container.find(".qi-export-modal").remove();

      // Add new modal
      const $modal = $(modalHtml).appendTo(this.$container);

      // Show modal with animation
      setTimeout(() => $modal.addClass("show"), 10);

      // Handle close button and outside click
      $modal
        .find(".qi-export-modal-close")
        .on("click", () => this.hideExportModal());
      $modal.on("click", (e) => {
        if ($(e.target).hasClass("qi-export-modal")) {
          this.hideExportModal();
        }
      });

      // Handle export option selection
      $modal.find(".qi-export-option").on("click", (e) => {
        const format = $(e.currentTarget).data("format");
        const exportType = $modal
          .find('input[name="qi-export-type"]:checked')
          .val();
        this.hideExportModal();
        this.exportTranscript(format, exportType);
      });
    }

    hideExportModal() {
      const $modal = this.$container.find(".qi-export-modal");
      $modal.removeClass("show");
      setTimeout(() => $modal.remove(), 300);
    }

    exportTranscript(format, exportType) {
      const messages = this.$messages
        .find(".qi-message")
        .map((_, el) => {
          const $msg = $(el);
          return {
            content: $msg.find(".qi-message-content").text().trim(),
            type: $msg.hasClass("qi-user-message") ? "user" : "assistant",
            timestamp: $msg.data("timestamp") || Date.now(),
          };
        })
        .get();

      if (messages.length === 0) {
        this.showError("No messages to export");
        return;
      }

      $.ajax({
        url: qkbChatbot.ajax_url,
        type: "POST",
        data: {
          action: "qkb_export_transcript",
          nonce: qkbChatbot.nonce,
          format: format,
          export_type: exportType,
          messages: messages,
          assistant_id: this.currentAssistant,
        },
        xhrFields: {
          responseType: "blob",
        },
        success: (response) => {
          const blob = new Blob([response]);
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = `chat-transcript-${Date.now()}.${format}`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        error: (xhr, status, error) => {
          this.showError("Failed to export transcript");
        },
      });
    }

    handleFileSelect(file) {
      const validTypes = [
        "application/pdf",
        "text/plain",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ];

      if (!validTypes.includes(file.type)) {
        this.showUploadStatus(
          `Invalid file type: ${file.type}. Please upload a PDF, TXT, DOC, or DOCX file.`,
          "error"
        );
        return;
      }

      // Store the file reference to ensure it's available when submitting
      this.selectedFile = file;

      // Show the progress bar - scope all selectors to the container
      const $uploadProgress = this.$container.find(".upload-progress");
      const $progressBar = this.$container.find(".upload-progress-bar");
      $uploadProgress.show();
      $progressBar.css("width", "0%");

      // Simulate upload progress with a more natural curve
      let progress = 0;
      const uploadSim = setInterval(() => {
        // Use a logarithmic curve for more realistic progress simulation
        if (progress < 90) {
          progress += (90 - progress) / 10;
        } else {
          progress = 90; // Cap at 90% until complete
        }

        $progressBar.css("width", `${progress}%`);

        // When file is processed, complete to 100%
        if (progress >= 89.5) {
          clearInterval(uploadSim);

          // Simulate processing time
          setTimeout(() => {
            $progressBar.css("width", "100%");

            setTimeout(() => {
              this.showUploadStatus(
                `${file.name} uploaded successfully!`,
                "success"
              );
              // Keep the progress bar visible but completed
            }, 300);
          }, 500);
        }
      }, 100);

      // Update UI - scope all selectors to the container
      const $fileName = this.$container.find(".file-name");

      // Clear any existing content
      $fileName.empty();

      // Add file icon based on type
      let fileIcon = "file-alt";
      if (file.type.includes("pdf")) {
        fileIcon = "file-pdf";
      } else if (file.type.includes("word")) {
        fileIcon = "file-word";
      }

      // Create a more structured file name display
      $fileName.html(`
                <div class="file-icon">
                    <i class="fas fa-${fileIcon}"></i>
                </div>
                <div class="file-details">
                    <span class="file-name-text">${file.name}</span>
                    <span class="file-size">${this.formatFileSize(
                      file.size
                    )}</span>
                </div>
                <button type="button" class="file-remove-btn" title="Remove file">
                    <i class="fas fa-times"></i>
                </button>
            `);

      // Show the file name container
      $fileName.show();

      // Change the label text
      this.$container
        .find(".file-input-label")
        .html('<i class="fas fa-sync-alt"></i> Change File');

      // Add click handler for remove button
      $fileName.find(".file-remove-btn").on("click", (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Clear the file input
        this.$container.find("#kb-file").val("");

        // Clear the stored file reference
        this.selectedFile = null;

        // Hide and clear the file name container
        $fileName.hide().empty();

        // Reset the label
        this.$container
          .find(".file-input-label")
          .html('<i class="fas fa-cloud-upload-alt"></i> Choose a file');

        // Hide progress and status
        $uploadProgress.hide();
        this.$container.find(".upload-status").hide();
      });
    }

    // Helper method to format file size
    formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }

    showUploadStatus(message, type) {
      // Scope the selector to the container
      const $status = this.$container.find(".upload-status");

      // Clear any existing content and classes
      $status.removeClass("success error").addClass(type).empty();

      // Create a more structured status message
      const $icon = $('<div class="status-icon"></div>').html(
        `<i class="fas fa-${
          type === "success" ? "check-circle" : "exclamation-circle"
        }"></i>`
      );

      const $message = $('<div class="status-message"></div>').text(message);

      const $closeBtn = $(
        '<button class="close-status" title="Dismiss"></button>'
      ).html('<i class="fas fa-times"></i>');

      // Assemble the status message
      $status.append($icon, $message, $closeBtn);

      // Show with animation
      $status.fadeIn(300);

      // For success messages, auto-hide after delay
      if (type === "success") {
        setTimeout(() => {
          $status.fadeOut(300);
        }, 4000);
      }

      // Add click handler for close button
      $closeBtn.on("click", function () {
        $(this).parent().fadeOut(300);
      });
    }

    // --- Enhanced Method: Load Manage Knowledge Entries ---
    loadManageKnowledge() {
      // Show loading state
      const $list = this.$kbModal.find(".qi-manage-knowledge-list");
      $list.html(
        '<div class="qi-loading"><i class="fas fa-spinner fa-spin"></i> Loading knowledge entries...</div>'
      );

      $.ajax({
        url: qkbChatbot.ajax_url,
        type: "POST",
        data: {
          action: "qkb_list_knowledge",
          nonce: qkbChatbot.nonce,
        },
        success: (response) => {
          $list.empty();
          if (response.success) {
            if (response.data && response.data.length) {
              // Render a table with pagination using a helper method
              this.renderKnowledgeTable(response.data);
            } else {
              // Show empty state with button to add knowledge
              $list.html(`
                                <div class="qi-no-knowledge">
                                    <div class="qi-no-knowledge-icon">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <p>No knowledge entries found. Add some knowledge to get started.</p>
                                    <button type="button" class="qi-kb-add-knowledge-btn">
                                        <i class="fas fa-plus"></i> Add Knowledge
                                    </button>
                                </div>
                            `);

              // Add click handler for the add knowledge button
              $list.find(".qi-kb-add-knowledge-btn").on("click", () => {
                // Switch to the add knowledge tab
                this.$kbModal
                  .find('.qi-kb-tab[data-tab="add"]')
                  .trigger("click");
              });
            }
          } else {
            this.showError(response.data || "Failed to load knowledge entries");
          }
        },
        error: (xhr, status, error) => {
          console.error("Failed to load knowledge entries:", error);
          this.showError("Failed to load knowledge entries");
        },
      });
    }

    // --- New Method: Render Knowledge Table with Pagination ---
    renderKnowledgeTable(data) {
      const pageSize = 5;
      let currentPage = 1;
      const totalPages = Math.ceil(data.length / pageSize);
      const $list = this.$kbModal.find(".qi-manage-knowledge-list");

      const renderPage = (page) => {
        $list.empty();
        // Create table structure
        const $table = $(`
                    <table class="qi-knowledge-table">
                        <thead>
                            <tr>
                                <th>Uploaded</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                `);

        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const pageData = data.slice(start, end);
        pageData.forEach((item) => {
          const $row = $(`
                        <tr class="qi-knowledge-item" data-id="${item.id}">
                            <td class="qi-knowledge-title">${item.title}</td>
                            <td class="qi-knowledge-date">${item.date}</td>
                            <td>
                                <button class="qi-knowledge-delete" title="Delete Knowledge">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </td>
                        </tr>
                    `);
          $table.find("tbody").append($row);
        });
        $table.hide().appendTo($list).fadeIn(300);

        // Add pagination controls
        const $pagination = $('<div class="qi-knowledge-pagination"></div>');
        const $prevBtn = $(`<button class="qi-pagination-prev">Prev</button>`);
        const $nextBtn = $(`<button class="qi-pagination-next">Next</button>`);
        if (page <= 1) {
          $prevBtn.prop("disabled", true);
        }
        if (page >= totalPages) {
          $nextBtn.prop("disabled", true);
        }
        $pagination.append($prevBtn, $nextBtn);
        $pagination.hide().appendTo($list).fadeIn(300);

        // Bind pagination button events
        $prevBtn.on("click", () => {
          if (currentPage > 1) {
            currentPage--;
            renderPage(currentPage);
          }
        });
        $nextBtn.on("click", () => {
          if (currentPage < totalPages) {
            currentPage++;
            renderPage(currentPage);
          }
        });
      };

      renderPage(currentPage);
    }

    updateCharacterCount() {
      const remaining = this.maxCharacters - this.$input.val().length;
      this.$characterCount.text(remaining);

      if (remaining <= 50) {
        this.$characterCount.addClass("qi-limit-warning");
      } else {
        this.$characterCount.removeClass("qi-limit-warning");
      }

      if (remaining <= 0) {
        this.$characterCount.addClass("qi-limit-exceeded");
        this.$input.val(this.$input.val().substring(0, this.maxCharacters));
      } else {
        this.$characterCount.removeClass("qi-limit-exceeded");
      }
    }

    // Note: editUserMessage and deleteUserMessage functions have been removed
    // as they are no longer used since edit/delete buttons were removed from user messages

    /**
     * Update a message in the chat history
     * @param {number} messageId - The ID of the message to update
     * @param {string} newContent - The new content for the message
     */
    updateMessageInHistory(messageId, newContent) {
      if (!this.currentAssistant || !this.chatHistory[this.currentAssistant]) {
        return;
      }

      // Find and update the message in the chat history
      const messageIndex = this.chatHistory[this.currentAssistant].findIndex(
        (msg) => msg.timestamp === messageId
      );

      if (messageIndex !== -1) {
        // Update the user message content
        this.chatHistory[this.currentAssistant][messageIndex].content =
          newContent;

        // If there's an assistant response after this message, mark it as outdated
        // This will be updated when the new response comes in
        if (
          messageIndex + 1 < this.chatHistory[this.currentAssistant].length &&
          this.chatHistory[this.currentAssistant][messageIndex + 1].type ===
            "assistant"
        ) {
          // We'll update this when the new response comes in
          // For now, just mark it as being updated
          this.chatHistory[this.currentAssistant][
            messageIndex + 1
          ].isUpdating = true;
        }

        this.saveChatHistory();
      }
    }

    /**
     * Remove a message and its response from the chat history
     * @param {number} messageId - The ID of the message to remove
     */
    removeMessageFromHistory(messageId) {
      if (!this.currentAssistant || !this.chatHistory[this.currentAssistant]) {
        return;
      }

      // Find the message index
      const messageIndex = this.chatHistory[this.currentAssistant].findIndex(
        (msg) => msg.timestamp === messageId
      );

      if (messageIndex !== -1) {
        // Remove the message and the next message (assistant response) if it exists
        const messagesToRemove =
          this.chatHistory[this.currentAssistant][messageIndex + 1] &&
          this.chatHistory[this.currentAssistant][messageIndex + 1].type ===
            "assistant"
            ? 2
            : 1;

        this.chatHistory[this.currentAssistant].splice(
          messageIndex,
          messagesToRemove
        );
        this.saveChatHistory();
      }
    }

    /**
     * Copy message content to clipboard using modern Clipboard API
     * @param {jQuery} $message - The message element to copy
     */
    async copyMessageToClipboard($message) {
      try {
        const content = $message.find(".qi-message-content").text().trim();

        if (!content) {
          this.showError("No content to copy");
          return;
        }

        // Use modern Clipboard API if available
        if (navigator.clipboard?.writeText) {
          await navigator.clipboard.writeText(content);
          this.showCopySuccess($message);
        } else {
          // Fallback for older browsers
          await this.fallbackCopyToClipboard(content);
          this.showCopySuccess($message);
        }
      } catch (error) {
        console.error("Copy error:", error);
        this.showError("Failed to copy text to clipboard");
      }
    }

    /**
     * Apply syntax highlighting to code blocks in a message
     * @param {jQuery} $message - Message element containing code blocks
     */
    applySyntaxHighlighting($message) {
      try {
        // Check if Prism.js is available
        if (typeof Prism !== 'undefined') {
          const $codeBlocks = $message.find('code[class*="language-"]');
          $codeBlocks.each((_, codeElement) => {
            Prism.highlightElement(codeElement);
          });
        } else {
          // Fallback: Add basic styling classes if Prism.js is not available
          const $codeBlocks = $message.find('.qi-code-block code');
          $codeBlocks.addClass('qi-code-fallback');
        }
      } catch (error) {
        console.warn("Failed to apply syntax highlighting:", error);
      }
    }

    /**
     * Copy text to clipboard (utility method for code blocks)
     * @param {string} text - Text to copy
     * @returns {Promise} - Promise that resolves when copy is successful
     */
    async copyTextToClipboard(text) {
      if (!text) {
        throw new Error("No text to copy");
      }

      // Try modern clipboard API first
      if (this.features.clipboard && navigator.clipboard?.writeText) {
        try {
          await navigator.clipboard.writeText(text);
          return;
        } catch (error) {
          console.warn("Modern clipboard API failed, falling back to legacy method:", error);
        }
      }

      // Fallback to legacy method
      return this.fallbackCopyToClipboard(text);
    }

    /**
     * Fallback copy method for older browsers
     * @private
     * @param {string} content - Content to copy
     */
    async fallbackCopyToClipboard(content) {
      return new Promise((resolve, reject) => {
        const textarea = document.createElement("textarea");
        textarea.value = content;
        textarea.style.position = "fixed";
        textarea.style.left = "-9999px";
        textarea.style.top = "-9999px";
        textarea.setAttribute("readonly", "");

        document.body.appendChild(textarea);

        try {
          textarea.select();
          textarea.setSelectionRange(0, 99999); // For mobile devices

          // Note: execCommand is deprecated but needed for older browser support
          const successful = document.execCommand("copy");

          if (successful) {
            resolve();
          } else {
            reject(new Error("Copy command failed"));
          }
        } catch (err) {
          reject(err);
        } finally {
          document.body.removeChild(textarea);
        }
      });
    }

    // Show copy success indicator
    showCopySuccess($message) {
      const $copyButton = $message.find(".qi-copy-button");
      const originalHtml = $copyButton.html();

      // Change icon to checkmark
      $copyButton.html('<i class="fas fa-check"></i>');
      $copyButton.addClass("qi-copy-success");

      // Show a success notification
      this.showSuccess("Copied to clipboard");

      // Reset after 2 seconds
      setTimeout(() => {
        $copyButton.html(originalHtml);
        $copyButton.removeClass("qi-copy-success");
      }, 2000);
    }

    // Submit feedback for a message
    submitFeedback(messageId, feedbackType) {
      // Find the message element
      const $message = this.$messages.find(
        `.qi-message[data-message-id="${messageId}"]`
      );
      if (!$message.length) return;

      // Get the assistant response content
      const response = $message.find(".qi-message-content").text().trim();

      // Find the corresponding user message (previous user message)
      const $userMessage = $message.prevAll('.qi-user-message').first();
      let query = $userMessage.length ? $userMessage.find(".qi-message-content").text().trim() : '';

      // If we can't find the user message using prevAll, try looking in chat history
      if (!query && this.chatHistory && this.currentAssistant) {
        const history = this.chatHistory[this.currentAssistant];
        if (history && history.length >= 2) {
          // Find the last user message before this assistant message
          for (let i = history.length - 1; i >= 0; i--) {
            if (history[i].type === 'user') {
              query = history[i].content;
              break;
            }
          }
        }
      }

      // If still no query, try to get it from the most recent user message in the DOM
      if (!query) {
        const $allUserMessages = this.$messages.find('.qi-user-message');
        if ($allUserMessages.length > 0) {
          query = $allUserMessages.last().find(".qi-message-content").text().trim();
        }
      }

      // Convert feedback type to numeric value
      const feedbackValue = feedbackType === "positive" ? 1 : (feedbackType === "negative" ? -1 : 0);

      // Validate required fields
      if (!messageId || !query || !response) {
        console.error("Missing required fields for feedback submission:", {
          messageId: messageId,
          query: query,
          response: response,
          userMessageFound: $userMessage.length > 0,
          totalUserMessages: this.$messages.find('.qi-user-message').length,
          totalMessages: this.$messages.find('.qi-message').length,
          currentAssistant: this.currentAssistant,
          historyLength: this.chatHistory && this.chatHistory[this.currentAssistant] ? this.chatHistory[this.currentAssistant].length : 0
        });

        // Try to provide a more helpful error message
        if (!query) {
          this.showError("Unable to submit feedback: Could not find the original question. Please try asking a new question first.");
        } else {
          this.showError("Unable to submit feedback: missing required information");
        }
        return;
      }

      // Highlight the selected feedback button
      const $feedbackButtons = $message.find(".qi-feedback-button");
      $feedbackButtons.removeClass("active");

      const $selectedButton = $message.find(
        `.qi-feedback-button[data-feedback="${feedbackType}"]`
      );
      $selectedButton.addClass("active");

      // Show a thank you message
      this.showSuccess("Thank you for your feedback!");

      // Send feedback to server
      $.ajax({
        url: qkbChatbot.ajax_url,
        type: "POST",
        data: {
          action: "qkb_submit_feedback",
          nonce: qkbChatbot.nonce,
          message_id: messageId,
          feedback: feedbackValue,
          query: query,
          response: response,
          assistant_id: this.currentAssistant || 1,
        },
        success: (response) => {
          if (!response.success) {
            console.error("Failed to submit feedback:", response.data);
            this.showError("Failed to submit feedback: " + (response.data || "Unknown error"));
          } else {
            console.log("Feedback submitted successfully");
          }
        },
        error: (xhr, status, error) => {
          console.error("Failed to submit feedback:", {
            status: status,
            error: error,
            response: xhr.responseText
          });
          this.showError("Failed to submit feedback. Please try again.");
        },
      });
    }

    /**
     * Get the appropriate icon class for a file type
     * @param {string} fileType - The MIME type of the file
     * @returns {string} - The Font Awesome icon class
     */
    getFileIcon(fileType) {
      if (fileType.includes("pdf")) {
        return "fa-file-pdf";
      } else if (fileType.includes("word") || fileType.includes("doc")) {
        return "fa-file-word";
      } else if (fileType.includes("image")) {
        return "fa-file-image";
      } else if (fileType.includes("text")) {
        return "fa-file-alt";
      } else {
        return "fa-file";
      }
    }

    /**
     * Format file size in a human-readable format
     * @param {number} bytes - The file size in bytes
     * @returns {string} - The formatted file size
     */
    formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }
  }

  /**
   * Initialize chat when document is ready and all scripts are loaded
   * Includes proper error handling and cleanup
   */
  window.addEventListener("load", function () {
    try {
      const chatContainer = document.querySelector(".qi-chat-container");
      if (chatContainer) {
        // Initialize chat instance
        window.qiChat = new QiChat();

        // Add cleanup on page unload
        window.addEventListener("beforeunload", function () {
          if (window.qiChat && typeof window.qiChat.destroy === 'function') {
            window.qiChat.destroy();
          }
        });

        // Add visibility change handler for performance
        document.addEventListener("visibilitychange", function () {
          if (window.qiChat && document.hidden) {
            // Pause non-essential operations when page is hidden
            if (window.qiChat.resizeObserver) {
              window.qiChat.resizeObserver.disconnect();
            }
          } else if (window.qiChat && !document.hidden) {
            // Resume operations when page becomes visible
            if (window.qiChat.resizeObserver && window.qiChat.$container?.[0]) {
              window.qiChat.resizeObserver.observe(window.qiChat.$container[0]);
            }
          }
        });

        console.log("QiChat initialized and ready");
      }
    } catch (error) {
      console.error("Failed to initialize QiChat:", error);
    }
  });
})(jQuery);

/**
 * Particles.js initialization for welcome message background
 */
(function($) {
    'use strict';

    // Initialize particles.js when document is ready
    $(document).ready(function() {
        initializeParticlesBackground();



        // Listen for custom event to reinitialize particles
        $(document).on('reinitialize-particles', function() {
            console.log('Reinitializing particles.js');
            initializeParticlesBackground();
        });
    });

    /**
     * Initialize particles.js background for welcome message
     */
    window.initializeParticlesBackground = function() {
        // Check if particles.js is loaded and the container exists
        if (typeof particlesJS !== 'undefined' && $('#qi-particles-js').length) {
            console.log('Initializing particles.js background');

            // Get theme to determine colors
            const isDarkMode = $('.qi-chat-container').attr('data-theme') === 'dark';
            const particleColor = isDarkMode ? '#5ebbff' : '#4a6cf7';
            const lineColor = isDarkMode ? '#5ebbff' : '#4a6cf7';

            // Initialize particles with custom configuration
            particlesJS('qi-particles-js', {
                "particles": {
                    "number": {
                        "value": 80,
                        "density": {
                            "enable": true,
                            "value_area": 800
                        }
                    },
                    "color": {
                        "value": particleColor
                    },
                    "shape": {
                        "type": "circle",
                        "stroke": {
                            "width": 0,
                            "color": "#000000"
                        }
                    },
                    "opacity": {
                        "value": 0.3,
                        "random": true,
                        "anim": {
                            "enable": true,
                            "speed": 1,
                            "opacity_min": 0.1,
                            "sync": false
                        }
                    },
                    "size": {
                        "value": 3,
                        "random": true,
                        "anim": {
                            "enable": true,
                            "speed": 2,
                            "size_min": 0.1,
                            "sync": false
                        }
                    },
                    "line_linked": {
                        "enable": true,
                        "distance": 150,
                        "color": lineColor,
                        "opacity": 0.2,
                        "width": 1
                    },
                    "move": {
                        "enable": true,
                        "speed": 1,
                        "direction": "none",
                        "random": true,
                        "straight": false,
                        "out_mode": "out",
                        "bounce": false,
                        "attract": {
                            "enable": true,
                            "rotateX": 600,
                            "rotateY": 1200
                        }
                    }
                },
                "interactivity": {
                    "detect_on": "canvas",
                    "events": {
                        "onhover": {
                            "enable": true,
                            "mode": "grab"
                        },
                        "onclick": {
                            "enable": true,
                            "mode": "push"
                        },
                        "resize": true
                    },
                    "modes": {
                        "grab": {
                            "distance": 140,
                            "line_linked": {
                                "opacity": 0.5
                            }
                        },
                        "push": {
                            "particles_nb": 4
                        }
                    }
                },
                "retina_detect": true
            });
        }
    }

    /**
     * Update particles.js colors based on theme
     */
    window.updateParticlesColors = function() {
        const isDarkMode = $('.qi-chat-container').attr('data-theme') === 'dark';

        if (window.pJSDom && window.pJSDom.length > 0) {
            const particlesInstance = window.pJSDom[0].pJS;

            if (particlesInstance) {
                // Update particle colors based on theme
                const particleColor = isDarkMode ? '#5ebbff' : '#4a6cf7';
                const lineColor = isDarkMode ? '#5ebbff' : '#4a6cf7';

                particlesInstance.particles.color.value = particleColor;
                particlesInstance.particles.line_linked.color = lineColor;

                // Refresh particles
                particlesInstance.fn.particlesRefresh();
            }
        }
    }

})(jQuery);